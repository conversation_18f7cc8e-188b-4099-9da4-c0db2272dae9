# Debug Tools

This directory contains debugging utilities for the UniDet3D project.

## Available Tools

### debug_dataset_order.py

**Purpose**: Analyzes and validates the dataset order mapping in configuration files.

**Usage**:
```bash
cd /home/<USER>/repos/unidet3d
python tools/debug/debug_dataset_order.py
```

**What it does**:
- Loads the main configuration file
- Analyzes the order of datasets in `val_dataloader.dataset.datasets`
- Validates that the script's dataset mapping matches the actual config order
- Tests specific dataset selection scenarios
- Provides detailed output showing:
  - Dataset index, type, annotation file, and data root
  - Mapping validation (✓ for correct, ✗ for incorrect)
  - Specific test case results

**Sample Output**:
```
=== Dataset Order Analysis ===
Total datasets in config: 6

Index 0: ScanNetDetDataset
  ann_file: scannet_infos_val.pkl
  data_root: /home/<USER>/repos/unidet3d/data/scannet/
  Identified as: scannet

=== Script Mapping Test ===
Index 0: Expected 'scannet' -> Actual 'scannet' ✓
Index 1: Expected 's3dis' -> Actual 's3dis' ✓
...

=== Test Specific Case ===
User requests: scannet
Script maps to index: 0
Selected dataset ann_file: scannet_infos_val.pkl
This is actually: scannet
✓ CORRECT: Script would select the right dataset
```

**When to use**:
- When debugging dataset selection issues
- After modifying configuration files
- To verify that test scripts select the correct datasets
- When adding new datasets to the configuration

## Notes

- All debug scripts automatically handle path resolution from the `tools/debug/` directory
- Scripts require the same environment as the main UniDet3D project (Docker container)
- Debug tools are for development and troubleshooting only
