#!/usr/bin/env python3

import sys
import os
# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)
from mmengine.config import Config

# Load the config file
config_file = os.path.join(project_root, 'configs/unidet3d_1xb8_scannet_s3dis_multiscan_3rscan_scannetpp_arkitscenes.py')
cfg = Config.fromfile(config_file)

print("=== Dataset Order Analysis ===")
print(f"Total datasets in config: {len(cfg.val_dataloader.dataset.datasets)}")
print()

# Print each dataset with its index and identifying information
for i, ds in enumerate(cfg.val_dataloader.dataset.datasets):
    dataset_type = ds.get('type', 'Unknown')
    ann_file = ds.get('ann_file', 'No ann_file')
    data_root = ds.get('data_root', 'No data_root')
    
    print(f"Index {i}: {dataset_type}")
    print(f"  ann_file: {ann_file}")
    print(f"  data_root: {data_root}")
    
    # Try to identify the dataset from the ann_file or data_root
    if 'scannet' in ann_file.lower() and 'scannetpp' not in ann_file.lower():
        identified = 'scannet'
    elif 's3dis' in ann_file.lower():
        identified = 's3dis'
    elif 'multiscan' in ann_file.lower():
        identified = 'multiscan'
    elif '3rscan' in ann_file.lower():
        identified = '3rscan'
    elif 'scannetpp' in ann_file.lower():
        identified = 'scannetpp'
    elif 'arkitscenes' in ann_file.lower():
        identified = 'arkitscenes'
    else:
        identified = 'UNKNOWN'
    
    print(f"  Identified as: {identified}")
    print()

# Test the mapping used in the script
script_mapping = ['scannet', 's3dis', 'multiscan', '3rscan', 'scannetpp', 'arkitscenes']
print("=== Script Mapping Test ===")
for i, expected in enumerate(script_mapping):
    if i < len(cfg.val_dataloader.dataset.datasets):
        ds = cfg.val_dataloader.dataset.datasets[i]
        ann_file = ds.get('ann_file', 'No ann_file')
        
        # Identify actual dataset
        if 'scannet' in ann_file.lower() and 'scannetpp' not in ann_file.lower():
            actual = 'scannet'
        elif 's3dis' in ann_file.lower():
            actual = 's3dis'
        elif 'multiscan' in ann_file.lower():
            actual = 'multiscan'
        elif '3rscan' in ann_file.lower():
            actual = '3rscan'
        elif 'scannetpp' in ann_file.lower():
            actual = 'scannetpp'
        elif 'arkitscenes' in ann_file.lower():
            actual = 'arkitscenes'
        else:
            actual = 'UNKNOWN'
        
        match = "✓" if expected == actual else "✗"
        print(f"Index {i}: Expected '{expected}' -> Actual '{actual}' {match}")
    else:
        print(f"Index {i}: Expected '{expected}' -> OUT OF RANGE")

print()
print("=== Test Specific Case ===")
target_dataset = 'scannet'
if target_dataset in script_mapping:
    dataset_idx = script_mapping.index(target_dataset)
    print(f"User requests: {target_dataset}")
    print(f"Script maps to index: {dataset_idx}")
    
    if dataset_idx < len(cfg.val_dataloader.dataset.datasets):
        selected_ds = cfg.val_dataloader.dataset.datasets[dataset_idx]
        ann_file = selected_ds.get('ann_file', 'No ann_file')
        print(f"Selected dataset ann_file: {ann_file}")
        
        # Identify what this actually is
        if 'scannet' in ann_file.lower() and 'scannetpp' not in ann_file.lower():
            actual = 'scannet'
        elif 's3dis' in ann_file.lower():
            actual = 's3dis'
        elif 'multiscan' in ann_file.lower():
            actual = 'multiscan'
        elif '3rscan' in ann_file.lower():
            actual = '3rscan'
        elif 'scannetpp' in ann_file.lower():
            actual = 'scannetpp'
        elif 'arkitscenes' in ann_file.lower():
            actual = 'arkitscenes'
        else:
            actual = 'UNKNOWN'
        
        print(f"This is actually: {actual}")
        
        if target_dataset == actual:
            print("✓ CORRECT: Script would select the right dataset")
        else:
            print("✗ BUG: Script would select the wrong dataset!")
    else:
        print("✗ ERROR: Index out of range")
else:
    print(f"✗ ERROR: {target_dataset} not in script mapping")
