#!/usr/bin/env python3
"""
Quick test script for the updated JSON to PLY converter

This script provides a simple way to test the converter with the new batch-based approach.

Author: Generated for UniDet3D project
Date: 2024
"""

import os
import sys
import argparse

# Add parent directory to path to import converter
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'data_preprocess'))
from converter_json2ply import JSONToPLYConverter


def test_scene_discovery():
    """Test the scene discovery functionality."""
    print("="*60)
    print("TESTING SCENE DISCOVERY")
    print("="*60)
    
    # Test configuration
    config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './test_output',
        'batch_name_list': ["RS10_Batch_02", "RS10_Batch_03"],
        'tolerance': 1e-6,
        'num_workers': 1,
        'batch_size': 1,
        'target_categories': ['door', 'window'],
        'background_label': 0,
        'max_scenes': 3  # Limit for testing
    }
    
    try:
        converter = JSONToPLYConverter(config)
        scene_infos = converter.load_scene_names()
        
        print(f"Found {len(scene_infos)} scenes:")
        for i, scene_info in enumerate(scene_infos[:10]):  # Show first 10
            print(f"  {i+1}. {scene_info['scene_name']} (batch: {scene_info['batch_name']})")
        
        if len(scene_infos) > 10:
            print(f"  ... and {len(scene_infos) - 10} more scenes")
        
        return len(scene_infos) > 0
        
    except Exception as e:
        print(f"Error during scene discovery: {e}")
        return False


def test_single_scene_processing():
    """Test processing a single scene."""
    print("\n" + "="*60)
    print("TESTING SINGLE SCENE PROCESSING")
    print("="*60)

    config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './test_output',
        'batch_name_list': ["RS10_Batch_02"],
        'tolerance': 1e-6,
        'num_workers': 1,
        'batch_size': 1,
        'target_categories': ['door', 'window'],
        'background_label': 0,
        'max_scenes': 1
    }
    
    try:
        converter = JSONToPLYConverter(config)
        scene_infos = converter.load_scene_names()
        
        if not scene_infos:
            print("No scenes found for testing")
            return False
        
        # Test first scene
        test_scene = scene_infos[0]
        print(f"Testing scene: {test_scene['scene_name']} from batch {test_scene['batch_name']}")
        
        success = converter.process_single_scene(test_scene)
        
        if success:
            output_file = os.path.join(config['output_root'], f"{test_scene['scene_name']}.ply")
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"✓ Successfully created PLY file: {output_file} ({file_size} bytes)")
                return True
            else:
                print("✗ PLY file was not created")
                return False
        else:
            print("✗ Scene processing failed")
            return False
            
    except Exception as e:
        print(f"Error during single scene processing: {e}")
        return False


def test_max_scenes_limit():
    """Test the max_scenes limitation functionality."""
    print("\n" + "="*60)
    print("TESTING MAX_SCENES LIMIT")
    print("="*60)
    
    config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './test_output',
        'batch_name_list': ["RS10_Batch_02"],
        'tolerance': 1e-6,
        'num_workers': 1,
        'batch_size': 1,
        'target_categories': ['door', 'window'],
        'background_label': 0,
        'max_scenes': 2  # Limit to 2 scenes
    }
    
    try:
        converter = JSONToPLYConverter(config)
        scene_infos = converter.load_scene_names()
        
        print(f"max_scenes set to 2, got {len(scene_infos)} scenes")
        
        if len(scene_infos) <= 2:
            print("✓ max_scenes limit working correctly")
            return True
        else:
            print("✗ max_scenes limit not working")
            return False
            
    except Exception as e:
        print(f"Error during max_scenes test: {e}")
        return False


def test_batch_configuration():
    """Test different batch configurations."""
    print("\n" + "="*60)
    print("TESTING BATCH CONFIGURATION")
    print("="*60)
    
    # Test with single batch
    config1 = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './test_output',
        'batch_name_list': ["RS10_Batch_02"],  # Single batch
        'max_scenes': 5
    }
    
    # Test with multiple batches
    config2 = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './test_output',
        'batch_name_list': ["RS10_Batch_02", "RS10_Batch_03"],  # Multiple batches
        'max_scenes': 5
    }
    
    try:
        print("Testing single batch configuration...")
        converter1 = JSONToPLYConverter(config1)
        scenes1 = converter1.load_scene_names()
        print(f"Single batch: found {len(scenes1)} scenes")
        
        print("Testing multiple batch configuration...")
        converter2 = JSONToPLYConverter(config2)
        scenes2 = converter2.load_scene_names()
        print(f"Multiple batches: found {len(scenes2)} scenes")
        
        if len(scenes2) >= len(scenes1):
            print("✓ Multiple batch configuration working correctly")
            return True
        else:
            print("✗ Multiple batch configuration issue")
            return False
            
    except Exception as e:
        print(f"Error during batch configuration test: {e}")
        return False


def test_rgb_intensity_options():
    """Test RGB and intensity control options."""
    print("\n" + "="*60)
    print("TESTING RGB AND INTENSITY OPTIONS")
    print("="*60)

    test_configs = [
        {
            'name': 'Full mode (RGB + Intensity)',
            'config': {
                'save_rgb': True,
                'save_intensity': True
            }
        },
        {
            'name': 'No RGB mode',
            'config': {
                'save_rgb': False,
                'save_intensity': True
            }
        },
        {
            'name': 'No Intensity mode',
            'config': {
                'save_rgb': True,
                'save_intensity': False
            }
        },
        {
            'name': 'Minimal mode (No RGB, No Intensity)',
            'config': {
                'save_rgb': False,
                'save_intensity': False
            }
        }
    ]

    base_config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './test_output',
        'batch_name_list': ["RS10_Batch_02"],
        'tolerance': 1e-6,
        'num_workers': 1,
        'batch_size': 1,
        'target_categories': ['door', 'window'],
        'background_label': 0,
        'max_scenes': 1
    }

    results = {}

    for test_case in test_configs:
        print(f"\nTesting: {test_case['name']}")

        # Merge configurations
        config = {**base_config, **test_case['config']}

        try:
            converter = JSONToPLYConverter(config)
            scene_infos = converter.load_scene_names()

            if not scene_infos:
                print(f"✗ No scenes found")
                results[test_case['name']] = False
                continue

            test_scene = scene_infos[0]
            success = converter.process_single_scene(test_scene)

            if success:
                output_file = os.path.join(config['output_root'], f"{test_scene['scene_name']}.ply")
                if os.path.exists(output_file):
                    # Validate PLY file format
                    with open(output_file, 'r') as f:
                        lines = f.readlines()

                    # Count properties
                    property_count = sum(1 for line in lines if line.startswith('property'))
                    expected_count = 3  # x, y, z
                    if config['save_rgb']:
                        expected_count += 3  # red, green, blue
                    if config['save_intensity']:
                        expected_count += 1  # intensity
                    expected_count += 2  # semantic_label, instance_label

                    if property_count == expected_count:
                        print(f"✓ {test_case['name']}: {property_count} properties as expected")
                        results[test_case['name']] = True
                    else:
                        print(f"✗ {test_case['name']}: Expected {expected_count} properties, got {property_count}")
                        results[test_case['name']] = False
                else:
                    print(f"✗ {test_case['name']}: Output file not created")
                    results[test_case['name']] = False
            else:
                print(f"✗ {test_case['name']}: Processing failed")
                results[test_case['name']] = False

        except Exception as e:
            print(f"✗ {test_case['name']}: Exception - {e}")
            results[test_case['name']] = False

    return all(results.values())


def main():
    """Run quick tests."""
    parser = argparse.ArgumentParser(description='Quick test for JSON to PLY converter')
    parser.add_argument('--test', choices=['discovery', 'single', 'max_scenes', 'batch', 'rgb_intensity', 'all'],
                       default='all', help='Which test to run')

    args = parser.parse_args()
    
    print("JSON to PLY Converter - Quick Test")
    print("="*60)
    
    # Create test output directory
    os.makedirs('./test_output', exist_ok=True)
    
    results = {}
    
    if args.test in ['discovery', 'all']:
        results['discovery'] = test_scene_discovery()
    
    if args.test in ['single', 'all']:
        results['single'] = test_single_scene_processing()
    
    if args.test in ['max_scenes', 'all']:
        results['max_scenes'] = test_max_scenes_limit()
    
    if args.test in ['batch', 'all']:
        results['batch'] = test_batch_configuration()

    if args.test in ['rgb_intensity', 'all']:
        results['rgb_intensity'] = test_rgb_intensity_options()

    # Print summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed")
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
