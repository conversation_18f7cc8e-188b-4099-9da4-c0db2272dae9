#!/usr/bin/env python3
"""
Test script for JSON to PLY converter

This script provides various testing and debugging utilities for the converter_json2ply.py script.

Author: Generated for UniDet3D project
Date: 2024
"""

import os
import sys
import json
import numpy as np
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional

# Add parent directory to path to import converter
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'data_preprocess'))
from converter_json2ply import JSONToPLYConverter, create_config_from_args


class ConverterTester:
    """Test and debug utilities for the JSON to PLY converter."""
    
    def __init__(self, config: Dict):
        """Initialize the tester with configuration."""
        self.config = config
        self.converter = JSONToPLYConverter(config)
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logger for testing."""
        logger = logging.getLogger('ConverterTester')
        logger.setLevel(logging.DEBUG)
        
        # Console handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def test_file_structure_loading(self) -> bool:
        """Test loading of file structure."""
        self.logger.info("Testing file structure loading...")
        
        try:
            scene_names = self.converter.load_file_structure()
            self.logger.info(f"Loaded {len(scene_names)} scenes")
            
            if scene_names:
                self.logger.info(f"First 5 scenes: {scene_names[:5]}")
                return True
            else:
                self.logger.error("No scenes loaded")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to load file structure: {e}")
            return False
    
    def test_filename_conversion(self, scene_names: List[str]) -> bool:
        """Test filename conversion from new to old format."""
        self.logger.info("Testing filename conversion...")
        
        success_count = 0
        for scene_name in scene_names[:5]:  # Test first 5 scenes
            try:
                old_name = self.converter._convert_new_to_old_filename(scene_name)
                self.logger.info(f"Converted: {scene_name} -> {old_name}")
                success_count += 1
            except Exception as e:
                self.logger.error(f"Failed to convert {scene_name}: {e}")
        
        self.logger.info(f"Successfully converted {success_count}/{min(5, len(scene_names))} filenames")
        return success_count > 0
    
    def test_json_loading(self, scene_name: str) -> bool:
        """Test JSON annotation loading for a specific scene."""
        self.logger.info(f"Testing JSON loading for scene: {scene_name}")
        
        try:
            coords, semantic, instance = self.converter.load_json_annotations(scene_name)
            
            self.logger.info(f"Loaded {len(coords)} points")
            self.logger.info(f"Coordinate range: X[{coords[:, 0].min():.2f}, {coords[:, 0].max():.2f}], "
                           f"Y[{coords[:, 1].min():.2f}, {coords[:, 1].max():.2f}], "
                           f"Z[{coords[:, 2].min():.2f}, {coords[:, 2].max():.2f}]")
            self.logger.info(f"Semantic labels: {np.unique(semantic)}")
            self.logger.info(f"Instance labels: {np.unique(instance)}")
            
            return len(coords) > 0
            
        except Exception as e:
            self.logger.error(f"Failed to load JSON for {scene_name}: {e}")
            return False
    
    def test_rgb_loading(self, scene_name: str) -> bool:
        """Test RGB/intensity data loading for a specific scene."""
        self.logger.info(f"Testing RGB loading for scene: {scene_name}")
        
        try:
            coords, attributes = self.converter.load_rgb_intensity_data(scene_name)
            
            self.logger.info(f"Loaded {len(coords)} RGB points")
            if len(coords) > 0:
                self.logger.info(f"RGB range: R[{attributes['red'].min()}, {attributes['red'].max()}], "
                               f"G[{attributes['green'].min()}, {attributes['green'].max()}], "
                               f"B[{attributes['blue'].min()}, {attributes['blue'].max()}]")
                self.logger.info(f"Intensity range: [{attributes['intensity'].min():.2f}, {attributes['intensity'].max():.2f}]")
            
            return len(coords) > 0
            
        except Exception as e:
            self.logger.error(f"Failed to load RGB for {scene_name}: {e}")
            return False
    
    def test_bounding_box_loading(self, scene_name: str) -> bool:
        """Test bounding box loading for a specific scene."""
        self.logger.info(f"Testing bounding box loading for scene: {scene_name}")
        
        try:
            bbox = self.converter.load_room_bounding_box(scene_name)
            
            if bbox:
                self.logger.info(f"Bounding box: {bbox}")
                return True
            else:
                self.logger.warning(f"No bounding box found for {scene_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to load bounding box for {scene_name}: {e}")
            return False
    
    def test_single_scene_processing(self, scene_name: str) -> bool:
        """Test complete processing of a single scene."""
        self.logger.info(f"Testing complete processing for scene: {scene_name}")
        
        try:
            success = self.converter.process_single_scene(scene_name)
            
            if success:
                self.logger.info(f"Successfully processed {scene_name}")
                
                # Check if output file exists
                output_path = os.path.join(self.config['output_root'], f"{scene_name}.ply")
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    self.logger.info(f"Output file created: {output_path} ({file_size} bytes)")
                else:
                    self.logger.warning(f"Output file not found: {output_path}")
                    return False
            else:
                self.logger.error(f"Failed to process {scene_name}")
                return False
            
            return success
            
        except Exception as e:
            self.logger.error(f"Exception during processing {scene_name}: {e}")
            return False
    
    def run_comprehensive_test(self, max_scenes: int = 3) -> Dict[str, bool]:
        """Run comprehensive test suite."""
        self.logger.info("="*50)
        self.logger.info("STARTING COMPREHENSIVE TEST SUITE")
        self.logger.info("="*50)
        
        results = {}
        
        # Test 1: File structure loading
        results['file_structure'] = self.test_file_structure_loading()
        
        if not results['file_structure']:
            self.logger.error("File structure loading failed, stopping tests")
            return results
        
        # Get scene names for further testing
        scene_names = self.converter.load_file_structure()
        if not scene_names:
            self.logger.error("No scenes available for testing")
            return results
        
        # Limit scenes for testing
        test_scenes = scene_names[:max_scenes]
        self.logger.info(f"Testing with scenes: {test_scenes}")
        
        # Test 2: Filename conversion
        results['filename_conversion'] = self.test_filename_conversion(test_scenes)
        
        # Test 3-6: Individual scene tests
        for i, scene_name in enumerate(test_scenes):
            self.logger.info(f"\n--- Testing scene {i+1}/{len(test_scenes)}: {scene_name} ---")
            
            results[f'json_loading_{i}'] = self.test_json_loading(scene_name)
            results[f'rgb_loading_{i}'] = self.test_rgb_loading(scene_name)
            results[f'bbox_loading_{i}'] = self.test_bounding_box_loading(scene_name)
            results[f'full_processing_{i}'] = self.test_single_scene_processing(scene_name)
        
        # Print summary
        self.logger.info("\n" + "="*50)
        self.logger.info("TEST RESULTS SUMMARY")
        self.logger.info("="*50)
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "PASS" if result else "FAIL"
            self.logger.info(f"{test_name}: {status}")
        
        self.logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        self.logger.info("="*50)
        
        return results
    
    def validate_ply_file(self, ply_path: str) -> bool:
        """Validate a PLY file format and content."""
        self.logger.info(f"Validating PLY file: {ply_path}")
        
        try:
            if not os.path.exists(ply_path):
                self.logger.error(f"PLY file does not exist: {ply_path}")
                return False
            
            with open(ply_path, 'r') as f:
                lines = f.readlines()
            
            # Check header
            if not lines[0].strip() == "ply":
                self.logger.error("Invalid PLY header")
                return False
            
            # Find vertex count
            vertex_count = 0
            header_end = 0
            for i, line in enumerate(lines):
                if line.startswith("element vertex"):
                    vertex_count = int(line.split()[-1])
                elif line.strip() == "end_header":
                    header_end = i + 1
                    break
            
            if vertex_count == 0:
                self.logger.error("No vertices found in PLY header")
                return False
            
            # Check data lines
            data_lines = len(lines) - header_end
            if data_lines != vertex_count:
                self.logger.error(f"Data line count mismatch: expected {vertex_count}, got {data_lines}")
                return False
            
            # Validate first few data lines
            for i in range(min(5, data_lines)):
                line = lines[header_end + i].strip()
                parts = line.split()
                if len(parts) != 9:  # x,y,z,r,g,b,intensity,semantic,instance
                    self.logger.error(f"Invalid data format at line {header_end + i + 1}: expected 9 values, got {len(parts)}")
                    return False
            
            self.logger.info(f"PLY file validation passed: {vertex_count} vertices")
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating PLY file: {e}")
            return False


def parse_test_arguments() -> argparse.Namespace:
    """Parse command line arguments for testing."""
    parser = argparse.ArgumentParser(description='Test JSON to PLY converter')
    
    parser.add_argument('--config_file', type=str, help='Configuration file path')
    parser.add_argument('--output_root', type=str, default='./test_output', help='Test output directory')
    parser.add_argument('--max_scenes', type=int, default=3, help='Maximum scenes to test')
    parser.add_argument('--scene_name', type=str, help='Specific scene to test')
    parser.add_argument('--validate_ply', type=str, help='PLY file to validate')
    
    # Default paths (can be overridden)
    parser.add_argument('--json_data_root', type=str, 
                       default='/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/')
    parser.add_argument('--rgb_data_root', type=str,
                       default='/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/')
    parser.add_argument('--floorplan_data_root', type=str,
                       default='/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/')
    parser.add_argument('--file_structure_path', type=str,
                       default='/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/03_done_data_file_structure.txt')
    
    return parser.parse_args()


def main():
    """Main test function."""
    args = parse_test_arguments()
    
    # Create test configuration
    config = {
        'json_data_root': args.json_data_root,
        'rgb_data_root': args.rgb_data_root,
        'floorplan_data_root': args.floorplan_data_root,
        'output_root': args.output_root,
        'file_structure_path': args.file_structure_path,
        'tolerance': 1e-6,
        'num_workers': 1,  # Use single worker for testing
        'batch_size': 1,
        'target_categories': ['door', 'window'],
        'background_label': 0
    }
    
    # Create output directory
    os.makedirs(args.output_root, exist_ok=True)
    
    # Create tester
    tester = ConverterTester(config)
    
    if args.validate_ply:
        # Validate specific PLY file
        success = tester.validate_ply_file(args.validate_ply)
        return 0 if success else 1
    
    elif args.scene_name:
        # Test specific scene
        success = tester.test_single_scene_processing(args.scene_name)
        return 0 if success else 1
    
    else:
        # Run comprehensive test
        results = tester.run_comprehensive_test(args.max_scenes)
        
        # Return success if most tests passed
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        success_rate = passed / total if total > 0 else 0
        
        return 0 if success_rate >= 0.7 else 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
