#!/usr/bin/env python3
"""
Example usage script for JSON to PLY converter

This script demonstrates how to use the converter_json2ply.py script with different configurations.

Author: Generated for UniDet3D project
Date: 2024
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(__file__))
from converter_json2ply import JSONToPLYConverter


def example_basic_usage():
    """Basic usage example with default settings."""
    print("="*60)
    print("EXAMPLE 1: Basic Usage")
    print("="*60)
    
    # Configuration for basic usage
    config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './output/basic_conversion',
        'file_structure_path': '/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/03_done_data_file_structure.txt',
        'tolerance': 1e-6,
        'num_workers': 4,
        'batch_size': 1,
        'target_categories': ['door', 'window'],
        'background_label': 0
    }
    
    # Create converter
    converter = JSONToPLYConverter(config)
    
    # Load scene names
    scene_names = converter.load_file_structure()
    
    if scene_names:
        print(f"Found {len(scene_names)} scenes to process")
        
        # Process first 3 scenes as example
        test_scenes = scene_names[:3]
        print(f"Processing first 3 scenes: {test_scenes}")
        
        converter.process_all_scenes(test_scenes)
    else:
        print("No scenes found to process")


def example_custom_categories():
    """Example with custom target categories."""
    print("\n" + "="*60)
    print("EXAMPLE 2: Custom Categories")
    print("="*60)
    
    config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './output/custom_categories',
        'file_structure_path': '/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/03_done_data_file_structure.txt',
        'tolerance': 1e-6,
        'num_workers': 2,
        'batch_size': 1,
        'target_categories': ['door', 'window', 'openings'],  # Include openings
        'background_label': 0
    }
    
    converter = JSONToPLYConverter(config)
    scene_names = converter.load_file_structure()
    
    if scene_names:
        # Process single scene for demonstration
        test_scene = scene_names[0]
        print(f"Processing single scene with custom categories: {test_scene}")
        
        success = converter.process_single_scene(test_scene)
        if success:
            print(f"Successfully processed {test_scene}")
        else:
            print(f"Failed to process {test_scene}")


def example_high_precision():
    """Example with high precision matching."""
    print("\n" + "="*60)
    print("EXAMPLE 3: High Precision Matching")
    print("="*60)
    
    config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './output/high_precision',
        'file_structure_path': '/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/03_done_data_file_structure.txt',
        'tolerance': 1e-8,  # Higher precision
        'num_workers': 1,   # Single worker for precision
        'batch_size': 1,
        'target_categories': ['door', 'window'],
        'background_label': 0
    }
    
    converter = JSONToPLYConverter(config)
    scene_names = converter.load_file_structure()
    
    if scene_names:
        test_scene = scene_names[0]
        print(f"Processing with high precision tolerance (1e-8): {test_scene}")
        
        success = converter.process_single_scene(test_scene)
        if success:
            print(f"High precision processing completed for {test_scene}")


def example_batch_processing():
    """Example of batch processing with multiprocessing."""
    print("\n" + "="*60)
    print("EXAMPLE 4: Batch Processing")
    print("="*60)
    
    config = {
        'json_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        'rgb_data_root': '/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        'floorplan_data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'output_root': './output/batch_processing',
        'file_structure_path': '/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/03_done_data_file_structure.txt',
        'tolerance': 1e-6,
        'num_workers': 8,   # Use more workers for batch processing
        'batch_size': 5,
        'target_categories': ['door', 'window'],
        'background_label': 0
    }
    
    converter = JSONToPLYConverter(config)
    scene_names = converter.load_file_structure()
    
    if scene_names:
        # Process first 10 scenes in batch
        batch_scenes = scene_names[:10]
        print(f"Batch processing {len(batch_scenes)} scenes with {config['num_workers']} workers")
        
        converter.process_all_scenes(batch_scenes)


def example_command_line_usage():
    """Show command line usage examples."""
    print("\n" + "="*60)
    print("COMMAND LINE USAGE EXAMPLES")
    print("="*60)
    
    examples = [
        {
            'title': 'Basic conversion',
            'command': 'python tools/data_preprocess/converter_json2ply.py --output_root ./output/basic'
        },
        {
            'title': 'Process specific scenes',
            'command': 'python tools/data_preprocess/converter_json2ply.py --output_root ./output/specific --scene_names scene1 scene2 scene3'
        },
        {
            'title': 'High precision with single worker',
            'command': 'python tools/data_preprocess/converter_json2ply.py --output_root ./output/precision --tolerance 1e-8 --num_workers 1'
        },
        {
            'title': 'Custom categories',
            'command': 'python tools/data_preprocess/converter_json2ply.py --output_root ./output/custom --target_categories door window openings'
        },
        {
            'title': 'Batch processing with 8 workers',
            'command': 'python tools/data_preprocess/converter_json2ply.py --output_root ./output/batch --num_workers 8 --max_scenes 20'
        },
        {
            'title': 'Test mode (first 3 scenes)',
            'command': 'python tools/data_preprocess/converter_json2ply.py --output_root ./output/test --max_scenes 3'
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   {example['command']}")


def example_testing_and_debugging():
    """Show testing and debugging examples."""
    print("\n" + "="*60)
    print("TESTING AND DEBUGGING EXAMPLES")
    print("="*60)
    
    test_examples = [
        {
            'title': 'Run comprehensive test suite',
            'command': 'python tools/debug/test_converter_json2ply.py --output_root ./test_output --max_scenes 3'
        },
        {
            'title': 'Test specific scene',
            'command': 'python tools/debug/test_converter_json2ply.py --scene_name "000_biguiyuanxingzuan_res-ff_RS10" --output_root ./test_output'
        },
        {
            'title': 'Validate PLY file',
            'command': 'python tools/debug/test_converter_json2ply.py --validate_ply ./output/scene.ply'
        }
    ]
    
    for i, example in enumerate(test_examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   {example['command']}")


def show_output_structure():
    """Show expected output structure."""
    print("\n" + "="*60)
    print("OUTPUT STRUCTURE")
    print("="*60)
    
    structure = """
output_directory/
├── scene1.ply                 # PLY file for scene1
├── scene2.ply                 # PLY file for scene2
├── scene3.ply                 # PLY file for scene3
└── ...

Each PLY file contains:
- Point coordinates (x, y, z)
- RGB colors (red, green, blue)
- Intensity values
- Semantic labels (wall=0, floor=1, ceiling=2, door=3, window=4, other=5)
- Instance labels (door/window instances with unique IDs, others=0)

Log files are saved to:
tools/data_preprocess/data_preprocess_log/converter_json2ply_log_YYYYMMDD.log
"""
    
    print(structure)


def main():
    """Run all examples."""
    print("JSON to PLY Converter - Usage Examples")
    print("="*60)
    
    # Check if we're in the right directory
    if not os.path.exists('tools/data_preprocess/converter_json2ply.py'):
        print("Error: Please run this script from the repository root directory")
        print("Current directory:", os.getcwd())
        return 1
    
    try:
        # Show command line examples (always works)
        example_command_line_usage()
        example_testing_and_debugging()
        show_output_structure()
        
        # Try to run actual examples (may fail if data paths don't exist)
        print("\n" + "="*60)
        print("ATTEMPTING TO RUN ACTUAL EXAMPLES")
        print("="*60)
        print("Note: These examples may fail if data paths are not accessible")
        
        try:
            example_basic_usage()
        except Exception as e:
            print(f"Basic usage example failed: {e}")
        
        try:
            example_custom_categories()
        except Exception as e:
            print(f"Custom categories example failed: {e}")
        
        try:
            example_high_precision()
        except Exception as e:
            print(f"High precision example failed: {e}")
        
        # Skip batch processing example to avoid long execution
        print("\nSkipping batch processing example to avoid long execution time")
        print("Use the command line example above to run batch processing")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        return 1
    
    print("\n" + "="*60)
    print("EXAMPLES COMPLETED")
    print("="*60)
    print("For more information, see:")
    print("- docs/data_conversion_requirements.md")
    print("- tools/data_preprocess/converter_json2ply.py --help")
    print("- tools/debug/test_converter_json2ply.py --help")
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
