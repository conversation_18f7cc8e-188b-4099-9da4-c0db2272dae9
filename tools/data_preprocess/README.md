# JSON到PLY点云数据转换工具

本工具用于将室内点云门窗检测数据集从JSON标注格式转换为PLY点云格式，支持可视化和后续分析。

## 功能特性

- ✅ 批量读取JSON标注文件
- ✅ 提取语义和实例标签信息
- ✅ 从RGB LAS文件获取颜色和强度信息
- ✅ 基于房间边界框过滤点云
- ✅ 生成包含完整信息的PLY文件
- ✅ 多进程并行处理
- ✅ 详细日志记录和错误处理
- ✅ 测试和调试工具

## 文件结构

```
tools/data_preprocess/
├── converter_json2ply.py          # 主转换脚本
├── example_usage.py               # 使用示例
├── README.md                      # 本文档
├── data_preprocess_log/           # 日志目录
├── density_map_gen/               # 辅助工具
│   ├── filename_converter.py     # 文件名转换工具
│   ├── generate_coco_hc_0722.py  # 参考实现
│   └── ...
└── process2_vis_las.py           # 参考实现

tools/debug/
└── test_converter_json2ply.py    # 测试脚本

docs/
└── data_conversion_requirements.md # 详细需求文档
```

## 快速开始

### 1. 基本使用

```bash
# 使用默认路径进行转换（输出到 data/hc3d/ply）
python tools/data_preprocess/converter_json2ply.py

# 查看帮助信息
python tools/data_preprocess/converter_json2ply.py --help
```

### 2. 自定义路径

```bash
python tools/data_preprocess/converter_json2ply.py \
    --json_data_root /path/to/json/annotations \
    --rgb_data_root /path/to/rgb/las/files \
    --floorplan_data_root /path/to/floorplan/data \
    --output_root ./output/custom
```

### 3. 处理特定场景

```bash
python tools/data_preprocess/converter_json2ply.py \
    --scene_names "000_scene1_res-ff_RS10" "001_scene2_res-uf_RS10"
```

### 4. 测试单个场景

```bash
# 只处理第一个场景进行测试
python tools/data_preprocess/converter_json2ply.py \
    --max_scenes 1
```

### 5. 多进程批量处理

```bash
python tools/data_preprocess/converter_json2ply.py \
    --num_workers 32 \
    --max_scenes 100
```

### 6. 控制RGB和intensity信息

```bash
# 仅保存坐标和标签（跳过RGB和intensity处理）
python tools/data_preprocess/converter_json2ply.py \
    --no_rgb --no_intensity

# 仅保存RGB，跳过intensity
python tools/data_preprocess/converter_json2ply.py \
    --no_intensity

# 仅保存intensity，跳过RGB
python tools/data_preprocess/converter_json2ply.py \
    --no_rgb
```

## 参数说明

### 输入路径参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--json_data_root` | `/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/` | JSON标注文件根目录 |
| `--rgb_data_root` | `/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/` | RGB LAS文件根目录 |
| `--floorplan_data_root` | `/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/` | floorplan.json文件根目录 |
| `--batch_name_list` | `['RS10_Batch_02', 'RS10_Batch_03', 'RS10_Batch_04', 'RS10_Batch_05']` | 要处理的批次列表 |

### 输出参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--output_root` | `data/hc3d/ply` | PLY文件输出目录 |

### 处理参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--tolerance` | `1e-6` | 点云匹配容差 |
| `--num_workers` | `4` | 并行处理进程数（最大64） |
| `--target_categories` | `['door', 'window']` | 需要提取实例标签的类别 |
| `--background_label` | `0` | 背景实例标签值 |

### RGB和Intensity控制参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--save_rgb` | `True` | 保存RGB颜色信息 |
| `--no_rgb` | - | 不保存RGB颜色信息 |
| `--save_intensity` | `True` | 保存强度信息 |
| `--no_intensity` | - | 不保存强度信息 |

### 场景选择参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--scene_names` | 无 | 指定要处理的场景名称 |
| `--max_scenes` | 无 | 限制处理的场景数量（测试时可设为1） |

## 输出格式

生成的PLY文件包含以下属性（根据参数可选）：

### 完整模式（默认）
```
property float x              # X坐标
property float y              # Y坐标
property float z              # Z坐标
property uchar red            # 红色分量 (0-255) - 可选
property uchar green          # 绿色分量 (0-255) - 可选
property uchar blue           # 蓝色分量 (0-255) - 可选
property float intensity      # 强度值 - 可选
property int semantic_label   # 语义标签
property int instance_label   # 实例标签
```

### 精简模式（--no_rgb --no_intensity）
```
property float x              # X坐标
property float y              # Y坐标
property float z              # Z坐标
property int semantic_label   # 语义标签
property int instance_label   # 实例标签
```

### 语义标签映射

- `0`: wall (墙)
- `1`: floor (地板)
- `2`: ceiling (天花板)
- `3`: door (门)
- `4`: window (窗)
- `5`: other (其他)

### 实例标签规则

- **门窗实例**: 使用公式 `file_index * 1000 + original_instance_id` 生成唯一ID
- **其他类别**: 设置为背景标签值 (默认0)

## 测试和调试

### 运行测试套件

```bash
# 综合测试（推荐）
python tools/debug/test_converter_json2ply.py \
    --output_root ./test_output \
    --max_scenes 3

# 测试特定场景
python tools/debug/test_converter_json2ply.py \
    --scene_name "000_biguiyuanxingzuan_res-ff_RS10" \
    --output_root ./test_output

# 验证PLY文件格式
python tools/debug/test_converter_json2ply.py \
    --validate_ply ./output/scene.ply
```

### 查看使用示例

```bash
# 运行示例脚本，查看各种使用方式
python tools/data_preprocess/example_usage.py
```

## 日志和监控

### 日志文件

- **位置**: `tools/data_preprocess/data_preprocess_log/converter_json2ply_log_YYYYMMDD.log`
- **格式**: 包含时间戳、进程ID、日志级别和详细信息
- **内容**: 处理状态、统计信息、错误详情

### 处理统计

脚本会输出以下统计信息：
- 总场景数
- 成功处理场景数
- 失败场景数
- 成功率
- 总处理点数

## 故障排除

### 常见问题

1. **路径不存在错误**
   ```
   解决方案：检查输入路径是否正确，确认文件权限
   RGB数据路径结构：/rgb_data_root/RS10/{batch_name}/{scene_name}/Las/
   ```

2. **点云匹配失败**
   ```
   解决方案：调整--tolerance参数（默认1e-6），检查坐标系统一致性
   查看日志中的匹配统计信息
   ```

3. **内存不足**
   ```
   解决方案：减少--num_workers，使用较小的批次大小
   对于大场景，考虑增加系统内存
   ```

4. **文件名转换失败**
   ```
   解决方案：检查文件名格式，查看日志详细错误信息
   确认新旧版本文件名格式一致
   ```

5. **类型错误**
   ```
   解决方案：JSON中的数值字段可能是字符串格式
   脚本已自动处理类型转换，如遇到新的类型错误，查看详细日志
   ```

### 性能优化建议

- 使用SSD存储提高I/O性能
- 根据CPU核心数调整`--num_workers`
- 对于大数据集，分批处理避免内存溢出
- 使用适当的`--tolerance`值平衡精度和性能

## 依赖项

确保安装以下Python包：

```bash
pip install numpy scipy laspy open3d
```

## 相关文档

- [详细需求文档](../../docs/data_conversion_requirements.md)
- [UniDet3D项目README](../../README_CN.md)

## 支持和反馈

如遇到问题或需要功能改进，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本验证环境配置
3. 参考故障排除部分
4. 检查输入数据格式和路径配置

## 成功案例

### 测试结果示例

#### 完整模式（包含RGB和intensity）
```bash
python tools/data_preprocess/converter_json2ply.py --max_scenes 1

# 输出结果：
# - 发现59个场景，处理1个场景
# - JSON数据：29,403,565个点
# - 点云匹配：42个点匹配成功
# - 边界框过滤：42个点保留
# - 生成PLY文件：data/hc3d/ply/001_cuishanlantian_res-ff_RS10.ply (9个属性)
# - 处理时间：67.27秒
# - 成功率：100%
```

#### 精简模式（仅坐标和标签）
```bash
python tools/data_preprocess/converter_json2ply.py --max_scenes 1 --no_rgb --no_intensity

# 输出结果：
# - RGB processing: disabled
# - Intensity processing: disabled
# - JSON数据：29,403,565个点
# - 跳过RGB匹配，保留所有JSON点
# - 边界框过滤：28,873,210个点保留
# - 生成PLY文件：data/hc3d/ply/001_cuishanlantian_res-ff_RS10.ply (5个属性)
# - 处理时间：115.09秒
# - 成功率：100%
```

### PLY文件格式验证

#### 完整模式（9个属性）
```
property float x, y, z          # 坐标
property uchar red, green, blue # RGB颜色 (0-255)
property float intensity        # 强度值
property int semantic_label     # 语义标签
property int instance_label     # 实例标签
```

#### 精简模式（5个属性）
```
property float x, y, z          # 坐标
property int semantic_label     # 语义标签
property int instance_label     # 实例标签
```

## 更新日志

- **v1.0**: 初始版本，支持基本JSON到PLY转换
- **v1.1**: 修复RGB路径问题和类型转换错误
- **v1.2**: 添加RGB和intensity可选控制功能
- 支持多进程处理和详细日志记录
- 包含完整的测试和调试工具
- 默认输出到 `data/hc3d/ply/` 目录
- 支持灵活的属性选择（RGB和intensity可选）
