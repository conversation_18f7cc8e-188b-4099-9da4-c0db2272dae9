"""
针对文件夹数量做多进程并行
适用于目前 batch2-batch5数据, to_label是未着色点云数据, rgb是着色点云数据
生成.las可视化文件, 放到SHARE平台中可视化
"""


import json
import numpy as np
import os
import glob
import argparse
import laspy
import time
import open3d as o3d
from scipy.spatial import KDTree
from multiprocessing import Pool, cpu_count
from concurrent.futures import ProcessPoolExecutor
from itertools import repeat
from functools import partial
import logging
from multiprocessing import TimeoutError as MTimeoutError

# 添加语义分割的颜色映射
SEMANTIC_COLORS = {
    0: [255, 182, 193],  # 浅粉红
    1: [144, 238, 144],  # 浅绿色
    2: [173, 216, 230],  # 浅蓝色
    3: [255, 255, 224],  # 浅黄色
    4: [221, 160, 221],  # 浅紫色
    5: [175, 238, 238],  # 浅青色
    6: [255, 228, 196],  # 浅橙色/米色
    7: [240, 230, 140],  # 浅卡其
    8: [255, 222, 173],  # 浅棕色/纳瓦白
    9: [224, 255, 255],  # 浅青白
}

category_dict = {
    "wall":70,
    "floor":71,
    "ceiling":72,
    "beam":73,
    "column":74,
    "door":75,
    "window":76,
    "pipeline":77,
    "stairs":78,
    "handrail":79,
    "glasswall":80,
    "openings":81 ,
    "suspended-ceiling":82,
    
    "table":210,
    "chair":211,
    "closet":212,
    "light":213,
    "sofa":214,
    "person":215,
    "pet":216,
    "shelves":217,
    "switch":218,
    "plants":219,
    "noise":220,
    "baseboard":221,
    "upstand":222,
    "curtain":223,
    "others":255,
}
# category_list = list(category_dict.keys())
# class2label = {cls: i for i, cls in enumerate(category_list)}
# print(class2label)


# 定义映射关系
valid_list = {
    'wall':    0, 
    'floor':   1, 
    'ceiling': 2, 
    'door':    3, 
    'window':  4,
    'openings':5,
    'suspended-ceiling': 6,
    'noise'   :7,
    'not_care':8
}


valid_list2 = {
    'wall':    0, 
    'floor':   1, 
    'ceiling': 2, 
    'door':    3, 
    'window':  4,
    'openings':0,
    'suspended-ceiling': 2,
    'noise'   :5,
    'not_care':6
}

valid_list0724 = {
    'wall':    0, 
    'floor':   1, 
    'ceiling': 2, 
    'door':    3, 
    'window':  4,
    'openings':6,
    'suspended-ceiling': 2,
    'noise'   :5,
    'not_care':6
}
valid_list0727 = {
    'wall':    0, 
    'floor':   1, 
    'ceiling': 2, 
    'door':    3, 
    'window':  4,
    'openings':5,
    'suspended-ceiling': 2,
    'noise'   :5,
    'not_care':5
}
valid_list0820 = {
    'wall':    0, 
    'floor':   1, 
    'ceiling': 2, 
    'door':    3, 
    'window':  4,
    'openings':3,
    'suspended-ceiling': 2,
    'noise'   :5,
    'not_care':5
}

def write_log(msg):
    with open('/workspace/06_tools/process.log', 'a', encoding='utf-8') as f:
        f.write(msg.rstrip('\n') + '\n')

def create_valid_dict(category_dict, valid_list):
    """
    根据valid_list创建一个新的字典，将不在valid_list中的类别映射到'not_care'的值。

    :param category_dict: 原始的类别字典
    :param valid_list: 包含需要映射的类别的字典，格式为{'category': label, ..., 'not_care': label}
    :return: 新的字典，包含所有类别的新标签
    """
    valid_dict = {}
    
    for key in category_dict:
        if key in valid_list:
            # 如果在valid_list中，则使用valid_list中的值
            valid_dict[key] = valid_list[key]
        else:
            # 否则，使用'not_care'的值
            valid_dict[key] = valid_list['not_care']
    
    return valid_dict


def save_semantic_las(coords, colors, labels, output_path, other_label):
    """
    保存带有语义颜色的LAS文件
    
    Args:
        coords: (N, 3) 坐标数组
        labels: (N,) 标签数组
        output_path: 输出LAS文件路径
    """
    # 创建LAS文件
    header = laspy.header.LasHeader(point_format=2, version="1.2")
    las = laspy.create(point_format=2, file_version="1.2")
    
    # 设置坐标
    las.x = coords[:, 0]
    las.y = coords[:, 1]
    las.z = coords[:, 2]
    
    # 根据标签设置颜色
    # colors = np.zeros((len(labels), 3), dtype=np.uint16)
    for label in np.unique(labels):
        if label == other_label : continue
        if label in SEMANTIC_COLORS:
            mask = (labels == label)
            # LAS文件的RGB值范围是0-65535
            colors[mask] = np.array(SEMANTIC_COLORS[label]) * 256
    
    # 设置颜色
    las.red = colors[:, 0]
    las.green = colors[:, 1]
    las.blue = colors[:, 2]

    las.classification = labels.astype(np.uint8)
    
    # 保存文件
    las.write(output_path)

def write_ply_visualization(file_path, coords, colors, labels):
    """
    PLY文件分为两部分：头部（header）+ 数据（data）。
    头部你需要手动写（如本例中的 f.write("ply\\n") ...）
    数据部分其实就是一行一行的点的属性（如 x y z r g b label），本质就是一个文本表格。
    np.savetxt 可以非常高效地把一个二维数组写成文本，每行一个点，属性用空格分隔，正好符合PLY的ascii格式要求。
    
    Args:
        file_path: 输出文件路径
        coords: (N, 3) 坐标数组
        colors: (N, 3) RGB颜色数组 (0-255)
        labels: (N,) 标签ID数组
    """
    import open3d as o3d
    
    # 创建点云对象
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(coords.astype(np.float64))
    pcd.colors = o3d.utility.Vector3dVector(colors.astype(np.float64) / 255.0)  # 归一化到0-1
    
    # 添加标签作为自定义属性
    # 注意：open3d的ply写入不支持自定义属性，我们需要手动写入
    # 这里我们创建一个包含所有信息的ply文件
    
    # 手动写入ply文件，包含自定义属性
    with open(file_path, 'w') as f:
        # 写入ply头部
        f.write("ply\n")
        f.write("format ascii 1.0\n")
        f.write(f"element vertex {len(coords)}\n")
        f.write("property float x\n")
        f.write("property float y\n")
        f.write("property float z\n")
        f.write("property uchar red\n")
        f.write("property uchar green\n")
        f.write("property uchar blue\n")
        f.write("property int label_id\n")
        f.write("end_header\n")
        
        # 使用numpy的向量化操作写入数据
        # 将数据堆叠成 (N, 7) 的数组: [x, y, z, r, g, b, label]
        data_to_write = np.column_stack([
            coords.astype(np.float32),
            colors.astype(np.uint8),
            labels.astype(np.int32)
        ])
        
        # 使用numpy的savetxt进行向量化写入
        np.savetxt(f, data_to_write, fmt=['%.6f', '%.6f', '%.6f', '%d', '%d', '%d', '%d'])
    
    # print(f"可视化文件已保存: {file_path} ({len(coords)} 个点)")


def load_las(las_path): 
    las = laspy.read(las_path)

    # 提取点云数据
    x = las.x
    y = las.y
    z = las.z

    # 创建LAS点的坐标数组
    las_coords = np.vstack((x, y, z)).transpose()
    # intensity = las.intensity
    # rgb = np.vstack((las.red, las.green, las.blue)).transpose()


   # 初始化RGB值为-1
    red = np.ones_like(x, dtype=np.int8) * -1
    green = np.ones_like(y, dtype=np.int8) * -1
    blue = np.ones_like(z, dtype=np.int8) * -1

    if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):
        # 处理RGB
        red = las.red
        green = las.green
        blue = las.blue
        # 判断是否需要归一化
        rgb_max = max(red.max(), green.max(), blue.max())
        if rgb_max > 255:
            # 归一化到0~255
            red = (las.red / 65535.0 * 255).astype(np.uint8)
            green = (las.green / 65535.0 * 255).astype(np.uint8)
            blue = (las.blue / 65535.0 * 255).astype(np.uint8)
        else:
            red = red.astype(np.uint8)
            green = green.astype(np.uint8)
            blue = blue.astype(np.uint8)
    
    attributes = {
        'intensity': las.intensity,
        'red': red,
        'green': green,
        'blue': blue
    }


    return las_coords, attributes



def load_json(json_path, class2label):
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

        room_coords = np.empty((0,3))
        room_semantic_gt = np.empty((0,1))
        count = 0
        if 'frames' not in data:
            raise ValueError("miss frames object")
        
        json_info = data['frames'][0] # pop list

        required_keys = {'cameraInfoList', 'frameInfo', 'instances'}
        if not required_keys.issubset(json_info.keys()):
            missing = required_keys - json_info.keys()
            raise ValueError(f"Json {json_path} missing: {missing} elements")

        # 遍历每一个instance 
        for instance in json_info['instances']:
            id = instance['id']
            number = instance['number']
            category = instance['category']
            category_gt = class2label[category]  # 这个实例的类别
            # 处理shapes
            for shape in instance.get('shapes', []):
                type = shape['type']
                size = shape['size']
                # print(f"Number: {number}, Size: {size}")
                count += size

                shape_data = shape.get('shapeData', {})
            
                if isinstance(shape_data, dict):
                    x_data = shape_data.get('x', [])
                    y_data = shape_data.get('y', [])
                    z_data = shape_data.get('z', [])
                    if len(x_data) == len(y_data) == len(z_data) == size:

                        coords      = np.stack([x_data, y_data, z_data], axis=1)
                        semantic_gt = np.full((size, 1), category_gt)
                        # 累加
                        room_coords = np.vstack([room_coords, coords])
                        room_semantic_gt = np.vstack([room_semantic_gt, semantic_gt])
                        # print(all_points_coord.shape)
                else:
                    raise ValueError(f"invalid shape_data , json_path: {json_path}")
        
        
        # print('json-points-count: ', count)
        # print('room_coords: ', room_coords.shape)
        return room_coords, room_semantic_gt



def compute_normals_with_pca(point_cloud, radius=0.05, max_nn=30):
    """使用PCA计算点云法向量"""
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(point_cloud)
    
    # 计算邻域并应用PCA
    pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=radius, max_nn=max_nn)
    )
    pcd.normalize_normals()  # 归一化法向量
    
    return np.array(pcd.normals)

def transfer_rgb_to_las(
    las_coords, 
    las_attributes, 
    rgb_coords, 
    rgb_attribute, 
    tolerance=1e-6, 
    num_workers=8
):
    # 构建KDTree，用rgb_coords建树
    tree = KDTree(rgb_coords)
    distances, indices = tree.query(las_coords, k=1, workers=num_workers)
    mask = distances < tolerance

    # 新建RGB数组，默认0
    r = np.zeros(las_coords.shape[0], dtype=np.uint8)
    g = np.zeros(las_coords.shape[0], dtype=np.uint8)
    b = np.zeros(las_coords.shape[0], dtype=np.uint8)

    # 匹配到的点赋值
    r[mask] = rgb_attribute['red'][indices[mask]]
    g[mask] = rgb_attribute['green'][indices[mask]]
    b[mask] = rgb_attribute['blue'][indices[mask]]

    # 更新attributes
    las_attributes['red'] = r
    las_attributes['green'] = g
    las_attributes['blue'] = b
    return las_attributes


def match_pts_attributes_fast_numpy(
    json_coords, 
    semantic_gt, 
    las_coords, 
    attributes, 
    tolerance=1e-6, 
    num_workers=8
):
    """
    将LAS点云文件与JSON标注文件匹配, 为每个标注点添加RGB和强度信息
    是否支持RS10数据，还需要debug看一下(去除了12m外的点）
    """
    tree = KDTree(las_coords)
    
    # 查询所有点的最近邻索引和距离
    distances, indices = tree.query(json_coords, k=1, workers=num_workers)
    
    # 创建掩码来筛选匹配的点
    mask = distances < tolerance
    
    # 获取匹配的点的索引
    matched_indices = np.where(mask)[0]
    matched_las_indices = indices[matched_indices]
    
    # 直接用 numpy 批量取值
    r = attributes['red'][matched_las_indices]
    g = attributes['green'][matched_las_indices]
    b = attributes['blue'][matched_las_indices]
    intensity = attributes['intensity'][matched_las_indices]
    xyz = np.round(json_coords[matched_indices], decimals=4)
    label_id = semantic_gt[matched_indices, 0]
    
    # 拼成 (N, 8) 的数组
    matched_points_arr = np.column_stack((xyz, label_id, r, g, b, intensity))
    
    # 获取未匹配的点的索引
    unmatched_indices = np.where(~mask)[0]
    
    return matched_points_arr, unmatched_indices




def process_building(
    building,
    class2label,
    label_root,
    raw_root, 
    rgb_root,
    output_root,
):
    """处理单个 building 文件夹"""
    try:
        t0 = time.time()
        #--------------------------------------
        label_dir = os.path.join(label_root, building)        
        raw_dir = os.path.join(raw_root, building)        
        rgb_dir = os.path.join(rgb_root, building)     

        # check
        if not os.path.isdir(raw_dir):
            logging.warning(f"[{building}] 原始数据目录不存在，跳过: {raw_dir}")
            return
        if not os.path.isdir(os.path.join(rgb_dir, 'Las')):
            logging.warning(f"[{building}] RGB数据目录不存在，跳过: {rgb_dir}")
            return

        save_path = os.path.join(output_root, building)      
        # os.makedirs(save_path, exist_ok=True)
        #--------------------------------------
        # 获取Donedata中某文件所有json
        json_list = sorted(glob.glob(os.path.join(label_dir, "pcd/*.json")))
        if not json_list:
            logging.warning(f"[{building}] 在 {label_dir} 中没有找到 .json 文件，跳过 building。")
            return
            
        # rgb
        rgb_las_files = glob.glob(os.path.join(rgb_dir, "Las/*.las"))
        
        if len(rgb_las_files) != 1:
            logging.warning(f"[{building}] RGB las数量不为1: {rgb_las_files}，跳过 building。")
            return
        rgb_path = rgb_las_files[0]

        # raw
        las_dir = os.path.join(raw_dir, 'LAS')
        #--------------------------------------
        # 加载全局的rgb-las
        logging.info(f"[{building}] 正在加载全局 RGB LAS: {os.path.basename(rgb_path)}")
        rgb_coords, rgb_attributes = load_las(rgb_path)
        logging.info(f"[{building}] 全局 RGB LAS 加载完成，点数: {len(rgb_coords)}")
        #--------------------------------------

        for idx, json_path in enumerate(json_list):
            json_name = os.path.basename(json_path)
            las_name = json_name.replace('.json', '.las')
            npy_file  = las_name.replace(" ", "").replace(".las", "")
            dst_path = os.path.join(save_path, npy_file)
            # os.makedirs(dst_path, exist_ok=True)
            
            # 检查是否已经存在所有必要的npy文件
            coord_file = os.path.join(dst_path, "coord.npy")
            color_file = os.path.join(dst_path, "color.npy")
            segment_file = os.path.join(dst_path, "segment.npy")
            normal_file = os.path.join(dst_path, "normal.npy")
            intensity_file = os.path.join(dst_path, "strength.npy")
            
            vis_dir = os.path.join(output_root, "vis")
            # os.makedirs(vis_dir, exist_ok=True)
            # vis_file = os.path.join(vis_dir, f"{building}-{npy_file}-vis.ply")
            semantic_las_path = os.path.join(vis_dir, f"{building}-{npy_file}-vis.las")

            # 如果所有文件都存在，则跳过
            if (os.path.exists(coord_file) and os.path.exists(color_file) and 
                os.path.exists(segment_file) and os.path.exists(normal_file) and 
                os.path.exists(semantic_las_path) and os.path.exists(intensity_file) ):

                logging.warning(f"[{building}] 跳过已存在的文件: {npy_file}")
                continue
            
            las_path = os.path.join(las_dir, las_name)
            if not os.path.exists(las_path):
                logging.warning(f"[{building}] 原始 LAS 文件不存在，跳过: {las_path}")
                continue
            
            if os.path.exists(las_path) and os.path.exists(json_path):
                
                json_coords, semantic_gt = load_json(json_path, class2label)
                if len(json_coords) == 0:
                    logging.warning(f"[{building}] JSON 文件不包含点: {json_path}")
                    continue
                
                las_coords, las_attributes = load_las(las_path)
                logging.info(f"[{building}] 加载las文件成功: {las_path}")

                las_attributes = transfer_rgb_to_las(
                    las_coords, 
                    las_attributes, 
                    rgb_coords, 
                    rgb_attributes, 
                    tolerance=1e-2, 
                    num_workers=1 # 在多进程工作函数中，子任务应为单线程
                )
                logging.info(f"[{building}] transfer_rgb_to_las 成功: {las_path}")

                match_result, _ = match_pts_attributes_fast_numpy(
                    json_coords, 
                    semantic_gt, 
                    las_coords, 
                    las_attributes, 
                    tolerance=1e-6,
                    num_workers=1 # 在多进程工作函数中，子任务应为单线程
                )
                
                logging.info(f"[{building}] match_result 成功: {json_path}")

                if len(match_result) == 0:
                    logging.warning(f"[{building}] 匹配后无有效点: {json_path}")
                    continue

                room_coords = match_result[:, :3]
                room_semantic_gt = match_result[:, 3:4]
                room_colors = match_result[:, 4:7]
                intensity = match_result[:, 7]  # 强度值

                pca_normals = compute_normals_with_pca(room_coords, radius=0.1)
                logging.info(f"[{building}] pca_normals 成功: {json_path}")

                os.makedirs(dst_path, exist_ok=True)
                os.makedirs(vis_dir, exist_ok=True)
                np.save(coord_file, room_coords.astype(np.float32))
                np.save(color_file, room_colors.astype(np.uint8))
                np.save(segment_file, room_semantic_gt.astype(np.int16))
                np.save(normal_file, pca_normals.astype(np.float32))
                np.save(intensity_file, intensity.astype(np.float32))

                # write_ply_visualization(
                #     vis_file, 
                #     room_coords, 
                #     room_colors, 
                #     room_semantic_gt.flatten()
                # )

                # 保存语义LAS文件
                save_semantic_las(
                    room_coords,
                    room_colors,
                    room_semantic_gt.flatten(),
                    semantic_las_path , 
                    other_label = 5
                )
                logging.info(f"[{building}] 语义LAS文件保存成功: {semantic_las_path}")


                t1 = time.time()

                logging.info(
                    f"[{building}] 完成: {npy_file} | "
                    f"点数: {len(room_coords)} | "
                    f"耗时: {t1 - t0:.2f}s"
                )

    except Exception as e:
        logging.error(f"处理 {building} 时发生严重错误: {e}", exc_info=True)
                

def main_process():

    log_file = '/workspace/06_tools/process0724.log'
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(process)d - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    # 批次号单独设置
    batch_name_list = [
                    "RS10_Batch_02" ,
                    "RS10_Batch_03" ,
                    "RS10_Batch_04" ,
                    "RS10_Batch_05",
                    # "RS10_Batch_06",
                    # "RS10_Batch_07",
    ]  
    for batch_name in batch_name_list:
        logging.info(f"\n{'='*20} 开始处理批次: {batch_name} {'='*20}\n")

        # 路径配置
        label_root =  f"/workspace/03_done_data/RS10/{batch_name}"
        raw_root =    f"/workspace/02_to_label/RS10/{batch_name}"
        rgb_root =    f"/workspace/04_rgb_data/RS10/{batch_name}"
        dst_root =    f"/workspace/07_process_data/XZQ_RS10_0820_class6/{batch_name}"

        # class2label
        # class2label = create_valid_dict(category_dict, valid_list2) # 门洞会被归为wall类别
        # class2label = create_valid_dict(category_dict, valid_list0724)
        # class2label = create_valid_dict(category_dict, valid_list0727) # 6个类别，噪声也归为其他
        class2label = create_valid_dict(category_dict, valid_list0820) # 6个类别，噪声也归为其他, 门洞归为门类别

        # Load room information
        logging.info("正在扫描文件夹...")
        # buildings_list = sorted([
        #     f for f in os.listdir(label_root)
        #     if os.path.isdir(os.path.join(label_root, f))
        # ])

        skip_folders = [
        '001_huaqiaocheng_res-uf_RS10',
        ]
    
        buildings_list = sorted([
            f for f in os.listdir(label_root)
            if os.path.isdir(os.path.join(label_root, f)) and
            f not in skip_folders
        ])



        if not buildings_list:
            logging.warning(f"在 {label_root} 中没有找到任何 building 文件夹。")
            return

        logging.info(f"找到 {len(buildings_list)} 个待处理文件夹: {buildings_list}")

        # 设置多进程
        num_tasks = len(buildings_list)
        # 自定义上限为48，可以按需调整
        num_workers = min(num_tasks, cpu_count(), 48) 
        # num_workers = 1 
        logging.info(f"使用 {num_workers} 个进程进行处理...")

        # 使用 functools.partial 来固定 process_building 函数除第一个以外的其他参数
        worker_func = partial(
            process_building,
            class2label=class2label,
            label_root=label_root,
            raw_root=raw_root, 
            rgb_root=rgb_root,
            output_root=dst_root,
        )

        # # 创建进程池并分发任务
        # with Pool(processes=num_workers) as pool:
        #     pool.map(worker_func, buildings_list)
        

        with Pool(processes=num_workers) as pool:
            # 把结果迭代出来，每 600 秒超时一次（可按需调大）
            for idx, _ in enumerate(
                pool.imap_unordered(worker_func, buildings_list, chunksize=1)
            ):
                if idx % 1 == 0:      # 每 10 个打点一次，证明主进程没死
                    logging.info(f"[主进程] 已处理 {idx+1}/{len(buildings_list)} 个 building")

        logging.info(f"\n{'='*20} 批次 {batch_name} 处理完成 {'='*20}\n")

    logging.info("\n所有数据处理已完成！\n====================\n")

if __name__ == "__main__":
    main_process()
