import argparse
import json
import os
import sys
from tqdm import tqdm
import laspy
import numpy as np
import cv2
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from functools import partial
from hc_utils import generate_density, generate_density_optimized, parse_floor_plan_polys, generate_coco_dict, calculate_slopes, rot_normalize_vectorized_gt, convert_numpy_to_native

from common_utils import export_density, append_to_checklist, CHECKLIST_PATH


type2id = {'living room': 0, 'kitchen': 1, 'bedroom': 2, 'bathroom': 3, 'balcony': 4, 'corridor': 5,
            'dining room': 6, 'study': 7, 'studio': 8, 'store room': 9, 'garden': 10, 'laundry room': 11,
            'office': 12, 'basement': 13, 'garage': 14, 'undefined': 15, 'door': 16, 'window': 17}


GEN_DENSITY_MAP = True
# GEN_DENSITY_MAP = False


"""generate_coco_hc_0717
该脚本负责遍历 RS10 数据集，
1. 计算场景的旋转角度、生成密度图
2. 将房间/门/窗等矢量注释转换为 COCO 格式
3. 划分 train/val/test 并输出 json 与图片
4. 过程中若遇到特殊情况（如缺少 floorplan.json、斜率或坐标异常等）则写入 checklist.txt 方便后续排查。
"""

def config():
    """解析命令行参数。

    返回一个包含数据根目录、输出目录以及密度图分辨率等字段的 argparse.Namespace 对象。
    """
    a = argparse.ArgumentParser(description='Generate coco format data for HC3D')
    a.add_argument('--data_root', default='/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/', type=str, help='path to raw HC3D_panorama folder')
    a.add_argument('--filelists_dir', default='/home/<USER>/data/RS10_data/00_dataset_spilt/filelists', type=str, help='path to filelists folder')
    a.add_argument('--output', default='/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_256x256/', type=str, help='path to output folder')
    a.add_argument('--density_height', default=1024, type=int, help='height of the density map')
    a.add_argument('--density_width', default=1024, type=int, help='width of the density map')
    a.add_argument('--downsample_factor', default=4, type=int, help='random downsample factor for point cloud; >1 will keep 1/factor of points')
    a.add_argument('--max_workers', default=32, type=int, help='number of parallel workers for LAS file processing')
    a.add_argument('--use_optimized_density', action='store_true', help='use optimized density generation function')
    a.add_argument('--loader_backend', choices=['thread', 'process', 'none'], default='thread', help='backend for LAS file processing: thread (default), process, or none for sequential')
    a.add_argument('--gen_test_set_only', action='store_true', help='generate only test set data (requires test.txt to exist)')

    args = a.parse_args()
    return args

def random_downsample(points: np.ndarray, downsample_factor: int = 4):
    """Randomly downsample 2-D (or 3-D) point array.

    Args:
        points (np.ndarray): shape (N, D) array of points.
        downsample_factor (int): keep 1/downsample_factor of total points.

    Returns:
        np.ndarray: downsampled points with same dimensionality.
    """
    if downsample_factor <= 1 or points.shape[0] == 0:
        return points

    n_points = points.shape[0]
    n_samples = max(1, n_points // downsample_factor)
    sampled_indices = np.random.choice(n_points, n_samples, replace=False)  # type: ignore
    downsampled_points = points[sampled_indices]
    # Debug info
    print(f"Downsample point cloud from {n_points} to {len(downsampled_points)} (factor={downsample_factor})")
    return downsampled_points


def process_single_las_file(scene_path: str, pointcloud_roi: list, rotation_matrix: np.ndarray,
                           downsample_factor: int = 4) -> np.ndarray:
    """Process a single LAS file with optimizations.

    Args:
        scene_path: Path to the LAS file
        pointcloud_roi: Bounding box for filtering points
        rotation_matrix: Pre-computed rotation matrix
        downsample_factor: Factor for downsampling during loading

    Returns:
        np.ndarray: Processed and rotated 2D points
    """
    try:
        # 读取LAS文件
        las = laspy.read(scene_path)

        # 预先计算过滤条件，避免重复计算
        z_filter = (las.z > pointcloud_roi[2] + 0.25) & (las.z < pointcloud_roi[5] - 0.25)
        # z_filter = (las.z > pointcloud_roi[2]) & (las.z < pointcloud_roi[5])
        x_filter = (las.x > pointcloud_roi[0]) & (las.x < pointcloud_roi[3])
        y_filter = (las.y > pointcloud_roi[1]) & (las.y < pointcloud_roi[4])
        combined_filter = z_filter & x_filter & y_filter

        # 如果没有符合条件的点，直接返回空数组
        if not np.any(combined_filter):
            return np.empty((0, 2), dtype=np.float32)

        # 提取符合条件的x, y坐标
        filtered_x = las.x[combined_filter]
        filtered_y = las.y[combined_filter]

        # 在读取时就进行下采样以减少内存使用
        if downsample_factor > 1:
            n_points = len(filtered_x)
            if n_points > 0:
                n_samples = max(1, n_points // downsample_factor)
                if n_samples < n_points:
                    indices = np.random.choice(n_points, n_samples, replace=False)
                    filtered_x = filtered_x[indices]
                    filtered_y = filtered_y[indices]

        # 构建点云矩阵，直接使用列堆叠避免转置
        filtered_point_cloud = np.column_stack((filtered_x, filtered_y))

        # 如果没有点，返回空数组
        if filtered_point_cloud.shape[0] == 0:
            return np.empty((0, 2), dtype=np.float32)

        # 直接使用 cos/sin 进行二维旋转，避免构造齐次坐标与大矩阵乘
        c = float(rotation_matrix[0, 0])
        s = float(rotation_matrix[0, 1])
        x = filtered_point_cloud[:, 0].astype(np.float32, copy=False)
        y = filtered_point_cloud[:, 1].astype(np.float32, copy=False)
        xr = x * c + y * s
        yr = -x * s + y * c
        rotated_points = np.column_stack((xr, yr))

        return rotated_points

    except Exception as e:
        print(f"Error processing {scene_path}: {e}")
        return np.empty((0, 2), dtype=np.float32)


def load_and_process_point_clouds_optimized(data_root: str, part: str, las_dir: str,
                                           pointcloud_roi: list, slope_angle: float,
                                           downsample_factor: int = 4,
                                           max_workers: int = 4,
                                           loader_backend: str = 'thread') -> np.ndarray:
    """Optimized point cloud loading and processing with parallel processing.

    Args:
        data_root: Root directory of the data
        part: Scene name/part
        las_dir: LAS directory name ('LAS' or 'LAS_Refined')
        pointcloud_roi: Bounding box for filtering
        slope_angle: Rotation angle
        downsample_factor: Downsampling factor
        max_workers: Number of parallel workers

    Returns:
        np.ndarray: Combined processed point cloud
    """
    las_folder = os.path.join(data_root, part, las_dir)
    if not os.path.exists(las_folder):
        return np.empty((0, 2), dtype=np.float32)

    # 获取所有LAS文件
    las_files = [f for f in os.listdir(las_folder) if f.endswith('.las')]
    if not las_files:
        return np.empty((0, 2), dtype=np.float32)

    # 预计算旋转矩阵
    rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope_angle), 1)

    # 估算总点数以预分配内存
    total_points_estimate = 0
    point_clouds = []

    print(f"Processing {len(las_files)} LAS files for scene {part}...")

    # 并行/串行处理LAS文件
    if loader_backend == 'none' or max_workers <= 1:
        for las_file in las_files:
            scene_path = os.path.join(las_folder, las_file)
            try:
                points = process_single_las_file(scene_path, pointcloud_roi, rotation_matrix, downsample_factor)
                if points.shape[0] > 0:
                    point_clouds.append(points)
                    total_points_estimate += points.shape[0]
            except Exception as e:
                print(f"Error processing {las_file}: {e}")
    else:
        ExecutorCls = ThreadPoolExecutor if loader_backend == 'thread' else ProcessPoolExecutor
        with ExecutorCls(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {}
            for las_file in las_files:
                scene_path = os.path.join(las_folder, las_file)
                future = executor.submit(process_single_las_file, scene_path, pointcloud_roi,
                                       rotation_matrix, downsample_factor)
                future_to_file[future] = las_file

            # 收集结果
            for future in as_completed(future_to_file):
                las_file = future_to_file[future]
                try:
                    points = future.result()
                    if points.shape[0] > 0:
                        point_clouds.append(points)
                        total_points_estimate += points.shape[0]
                except Exception as e:
                    print(f"Error processing {las_file}: {e}")

    # 合并所有点云
    if not point_clouds:
        return np.empty((0, 2), dtype=np.float32)

    # 使用 np.concatenate 而不是 np.vstack 提高效率
    all_points = np.concatenate(point_clouds, axis=0)

    print(f"Loaded {all_points.shape[0]} points from {len(point_clouds)} files")

    return all_points

# ------------------------------------------------------
# Utility: compute BGR mean & std over a set of folders
# ------------------------------------------------------

def compute_rgb_mean_std(image_dirs, exclude_suffix='_gt.png'):
    """Compute per-channel mean/std for BGR images under given directories.

    Args:
        image_dirs (List[str]): List of directories to search for images.
        exclude_suffix (str): Skip files whose name ends with this suffix.

    Returns:
        Tuple[np.ndarray, np.ndarray] | (None, None): mean & std arrays or
        (None, None) if no images are found.
    """
    channel_sum = np.zeros(3, dtype=np.float64)
    channel_sum_sq = np.zeros(3, dtype=np.float64)
    pixel_count = 0

    for d in image_dirs:
        if not os.path.isdir(d):
            continue
        for fname in os.listdir(d):
            if not fname.endswith('.png'):
                continue
            if exclude_suffix and fname.endswith(exclude_suffix):
                continue
            img_path = os.path.join(d, fname)
            img = cv2.imread(img_path)  # BGR
            if img is None:
                continue
            img = img.astype(np.float32)
            channel_sum += img.sum(axis=(0, 1))
            channel_sum_sq += (img ** 2).sum(axis=(0, 1))
            pixel_count += img.shape[0] * img.shape[1]

    if pixel_count == 0:
        return None, None

    mean = channel_sum / pixel_count
    var = channel_sum_sq / pixel_count - mean ** 2
    std = np.sqrt(var)
    return mean, std

def main(args):
    """主流程：遍历数据集并生成 COCO 标注。

    主要步骤：
        1) 遍历 data_root 下的子目录（一个子目录即一个扫描场景）
        2) 读取并验证 floorplan.json；如缺失则记录异常并跳过
        3) 计算矢量注释的整体倾斜角度（slope），得到 ROI
        4) 可选地读取激光点云并生成密度图
        5) 对矢量注释进行旋转及归一化，生成 COCO annotations
        6) 按 filelists_dir 将样本划分为 train / val / test
        7) 导出 json、密度图、可视化图，并记录 calibration 信息
    """

    data_root = args.data_root

    # --------------------------------------------------
    # Read train/val/test split lists from filelists dir
    # --------------------------------------------------
    filelists_dir = args.filelists_dir

    def _read_list(fname):
        """Utility to read lines from a txt file to a set (ignore empty lines)."""
        if not os.path.exists(fname):
            return set()
        with open(fname, 'r') as f:
            return set([l.strip() for l in f if l.strip()])

    train_scenes = _read_list(os.path.join(filelists_dir, 'train.txt'))
    val_scenes = _read_list(os.path.join(filelists_dir, 'val.txt'))
    test_scenes = _read_list(os.path.join(filelists_dir, 'test.txt'))

    generate_test = len(test_scenes) > 0

    # Handle test-only mode
    if args.gen_test_set_only:
        if not generate_test:
            raise ValueError("gen_test_set_only=True but test.txt is empty or missing")
        print(f"Test-only mode enabled. Processing only {len(test_scenes)} test scenes.")
        scenes_to_process = list(test_scenes)
        # In test-only mode, we don't need train/val scenes
        train_scenes = set()
        val_scenes = set()
    else:
        # Strictly process only scenes listed in train/val/test sets
        scenes_to_process = list(train_scenes | val_scenes | test_scenes)

    # Confirm existing dirs
    scenes_to_process = [s for s in scenes_to_process if os.path.isdir(os.path.join(data_root, s))]

    print(f'Total scenes to process (train/val/test): {len(scenes_to_process)}')

    ### prepare
    outFolder = args.output
    if not os.path.exists(outFolder):
        os.mkdir(outFolder)

    annotation_outFolder = os.path.join(outFolder, 'annotations')
    if not os.path.exists(annotation_outFolder):
        os.mkdir(annotation_outFolder)

    train_img_folder = os.path.join(outFolder, 'train')
    val_img_folder = os.path.join(outFolder, 'val')
    test_img_folder = os.path.join(outFolder, 'test')

    # Create folders based on mode
    if args.gen_test_set_only:
        folders_to_create = [test_img_folder]
    else:
        # Always create train/val folders; test folder only if needed
        folders_to_create = [train_img_folder, val_img_folder]
        if generate_test:
            folders_to_create.append(test_img_folder)

    for img_folder in folders_to_create:
        if not os.path.exists(img_folder):
            os.mkdir(img_folder)

    coco_train_json_path = os.path.join(annotation_outFolder, 'train.json')
    coco_val_json_path = os.path.join(annotation_outFolder, 'val.json')
    coco_test_json_path = os.path.join(annotation_outFolder, 'test.json')
    calibration_json_path = os.path.join(annotation_outFolder, 'calibration.json')

    # COCO format requires "info" and "licenses" fields. Provide empty placeholders
    empty_info = {}
    empty_licenses = []

    if args.gen_test_set_only:
        # Only create test dict in test-only mode
        coco_test_dict = {"info": empty_info, "licenses": empty_licenses,
                          "images":[],"annotations":[],"categories":[]}
        calibration_dict = {"calib": []}

        for key, value in type2id.items():
            type_dict = {"supercategory": "room", "id": value, "name": key}
            coco_test_dict["categories"].append(type_dict)
    else:
        coco_train_dict = {"info": empty_info, "licenses": empty_licenses,
                           "images":[],"annotations":[],"categories":[]}
        coco_val_dict = {"info": empty_info, "licenses": empty_licenses,
                         "images":[],"annotations":[],"categories":[]}
        coco_test_dict = {"info": empty_info, "licenses": empty_licenses,
                          "images":[],"annotations":[],"categories":[]}
        calibration_dict = {"calib": []}

        for key, value in type2id.items():
            type_dict = {"supercategory": "room", "id": value, "name": key}
            coco_train_dict["categories"].append(type_dict)
            coco_val_dict["categories"].append(type_dict)
            if generate_test:
                coco_test_dict["categories"].append(type_dict)

    ### begin processing
    instance_id = 0
    scene_id = 10000
    for part in tqdm(scenes_to_process):

        print('***** Processing part: ', part)
        gt_json_path = os.path.join(data_root, part, 'Annotations/floorplan.json')

        if not os.path.exists(gt_json_path):
            print(f'No floorplan.json found in {part}, skipping...')
            # 记录到 checklist
            append_to_checklist('缺少floorplan.json', part)
            continue

        # calculate the angle of slopes
        with open(gt_json_path, 'r') as f:
            vectorized_gt = json.load(f)
        slope_angle, max_coords, min_coords = calculate_slopes(vectorized_gt, type='mean')
        print("slope_angle: ", slope_angle, " max_coords: ", max_coords, " min_coords: ", min_coords)

        # 若斜率角度为 0 且最大/最小坐标全为 0，则认为 floorplan 信息异常，直接跳过并记录
        if slope_angle == 0 and np.count_nonzero(max_coords) == 0  and np.count_nonzero(min_coords) == 0:
            print(f'Skipping {part} due to zero slope angle and coordinates.')
            append_to_checklist('斜率坐标异常', part)
            continue

        # cloud points ROI
        pointcloud_roi = vectorized_gt['frames'][0]['boundingBox']
        print("pointcloud_roi: ", pointcloud_roi)
        # 确定LAS目录
        las_dir = 'LAS_Refined' if os.path.exists(os.path.join(data_root, part, 'LAS_Refined')) else 'LAS'

        # 使用优化的点云加载函数
        if GEN_DENSITY_MAP:
            # 在加载时就进行初步下采样以减少内存使用和处理时间
            initial_downsample = max(1, args.downsample_factor // 4)  # 初步下采样
            all_points = load_and_process_point_clouds_optimized(
                data_root, part, las_dir, pointcloud_roi, slope_angle,
                downsample_factor=initial_downsample, max_workers=args.max_workers,
                loader_backend=args.loader_backend
            )

            # 如果需要进一步下采样
            if args.downsample_factor > initial_downsample:
                remaining_factor = args.downsample_factor // initial_downsample
                all_points = random_downsample(all_points, remaining_factor)
        else:
            all_points = np.empty((0, 2), dtype=np.float32)

        ### project point cloud to density map
        if args.use_optimized_density:
            density, _ = generate_density_optimized(all_points, max_coords, min_coords, \
                                                   width=args.density_width, height=args.density_height)
        else:
            density, _ = generate_density(all_points, max_coords, min_coords, \
                                        width=args.density_width, height=args.density_height)
        scene_id += 1
        
        ### rescale raw annotations
        vectorized_gt, rot_gt_img = rot_normalize_vectorized_gt(vectorized_gt, slope_angle, max_coords, \
                                                    min_coords, np.array((args.density_height, args.density_width)))

        ### prepare coco dict
        img_id = int(scene_id)
        img_dict = {}
        img_dict["file_name"] = part + '.png'
        img_dict["id"] = img_id
        img_dict["width"] = args.density_width
        img_dict["height"] = args.density_height

        calib_dict = {
            "scene_name": part,
            "rotation_angle": slope_angle,
            "roi_max": max_coords,
            "roi_min": min_coords,
            "pointcloud_roi": pointcloud_roi  # 保存原始boundingBox信息
        }

        ### parse annotations
        polys = parse_floor_plan_polys(vectorized_gt, max_coords, min_coords, \
                                       np.array((args.density_height, args.density_width)))
        polygons_list = generate_coco_dict(vectorized_gt, polys, instance_id, img_id, ignore_types=['outwall'], scene_name=part)
        instance_id += len(polygons_list)

        if args.gen_test_set_only:
            # In test-only mode, all processed scenes are test scenes
            coco_test_dict["images"].append(img_dict)
            coco_test_dict["annotations"] += polygons_list
            export_density(density, test_img_folder, part)
            img_path = os.path.join(test_img_folder, f'{part}_gt.png')
            cv2.imwrite(img_path, rot_gt_img)
        else:
            ### train split defined by train.txt
            if part in train_scenes:
                coco_train_dict["images"].append(img_dict)
                coco_train_dict["annotations"] += polygons_list
                calibration_dict['calib'].append(calib_dict)
                export_density(density, train_img_folder, part)
                img_path = os.path.join(train_img_folder, f'{part}_gt.png')
                cv2.imwrite(img_path, rot_gt_img)

            ### val split defined by val.txt
            elif part in val_scenes:
                coco_val_dict["images"].append(img_dict)
                coco_val_dict["annotations"] += polygons_list
                export_density(density, val_img_folder, part)
                img_path = os.path.join(val_img_folder, f'{part}_gt.png')
                cv2.imwrite(img_path, rot_gt_img)

            # test split (only if enabled)
            elif generate_test and part in test_scenes:
                coco_test_dict["images"].append(img_dict)
                coco_test_dict["annotations"] += polygons_list
                export_density(density, test_img_folder, part)
                img_path = os.path.join(test_img_folder, f'{part}_gt.png')
                cv2.imwrite(img_path, rot_gt_img)
        
        print(scene_id)

    if args.gen_test_set_only:
        print("coco_test_json_path: ", coco_test_json_path)
        if len(coco_test_dict["images"]) > 0:
            with open(coco_test_json_path, 'w') as f:
                json.dump(coco_test_dict, f, default=convert_numpy_to_native)
    else:
        print("coco_train_json_path: ", coco_train_json_path)
        with open(coco_train_json_path, 'w') as f:
            json.dump(coco_train_dict, f, default=convert_numpy_to_native)
        with open(coco_val_json_path, 'w') as f:
            json.dump(coco_val_dict, f, default=convert_numpy_to_native)
        if generate_test and len(coco_test_dict["images"]) > 0:
            with open(coco_test_json_path, 'w') as f:
                json.dump(coco_test_dict, f, default=convert_numpy_to_native)

        with open(calibration_json_path, 'w') as f:
            json.dump(calibration_dict, f, default=convert_numpy_to_native)

    # ------------------------------------------------------------------
    # After dataset generation, compute BGR mean/std for train + val sets
    # (Skip in test-only mode since it's only needed for training)
    # ------------------------------------------------------------------

    if not args.gen_test_set_only:
        mean, std = compute_rgb_mean_std([train_img_folder, val_img_folder])

        if mean is not None:
            # Construct output path:
            #   <root>/00_dataset_spilt/rgb_mean_std/mean_rgb_std_<last_part>.txt
            last_part = os.path.basename(os.path.normpath(outFolder.rstrip(os.sep)))
            stats_dir = os.path.join(os.path.dirname(os.path.dirname(outFolder)),
                                     '00_dataset_spilt', 'rgb_mean_std')
            os.makedirs(stats_dir, exist_ok=True)
            stats_path = os.path.join(stats_dir, f'mean_rgb_std_{last_part}.txt')

            with open(stats_path, 'w') as f:
                f.write(f'mean_rgb = [{mean[0]:.2f}, {mean[1]:.2f}, {mean[2]:.2f}], # BGR mean\n')
                f.write(f'std_rgb  = [{std[0]:.2f}, {std[1]:.2f}, {std[2]:.2f}], # BGR std\n')

            print(f'RGB mean/std saved to {stats_path}')
        else:
            print('No images found for mean/std calculation. Skipped.')
    else:
        print('Skipping RGB mean/std calculation in test-only mode.')

if __name__ == "__main__":
    print('Generating coco format data for HC3D...')
    # 每次运行前清空 checklist.txt 内容，避免旧的异常信息干扰本次处理
    with open(CHECKLIST_PATH, 'w', encoding='utf-8') as f:
        pass
    main(config())