"""
This code is an adaptation that uses Structured 3D for the code base.

Reference: https://github.com/bertjiazheng/Structured3D
"""

import numpy as np
from shapely.geometry import Polygon
import os
import json
import sys
import cv2

from common_utils import resort_corners, append_to_checklist

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

type2id = {'living room': 0, 'kitchen': 1, 'bedroom': 2, 'bathroom': 3, 'balcony': 4, 'corridor': 5,
            'dining room': 6, 'study': 7, 'studio': 8, 'store room': 9, 'garden': 10, 'laundry room': 11,
            'office': 12, 'basement': 13, 'garage': 14, 'undefined': 15, 'door': 16, 'window': 17, 'room':2}


def piecewise_stretch(d, t1=None, t2=None, r1=1.0, p1=20, p2=70):
    """Piece-wise linear stretch with optional automatic thresholds.

    Args:
        d (np.ndarray): density map normalized to 0-1.
        t1 (float|None): lower threshold; if None, use p1 percentile of non-zero pixels.
        t2 (float|None): upper threshold; if None, use p2 percentile of non-zero pixels.
        r1 (float): slope for low-density region (<=t1).
        p1 (int): percentile to compute t1 when autoselecting.
        p2 (int): percentile to compute t2 when autoselecting (p2>p1).
    """

    # Auto-determine thresholds if needed
    if t1 is None or t2 is None:
        nz = d[d > 0]
        if nz.size == 0:
            return d  # all zeros, nothing to stretch
        if t1 is None:
            t1 = np.percentile(nz, p1)
        if t2 is None:
            t2 = np.percentile(nz, p2)

    # Fallback to sane defaults if thresholds degenerate
    if t2 <= t1:
        t2 = min(t1 + 1e-3, 0.99)

    y1 = r1 * t1
    r2 = (1.0 - y1) / (t2 - t1)

    d_stretched = np.empty_like(d)
    # 低密度
    mask1 = d <= t1
    d_stretched[mask1] = r1 * d[mask1]

    # 中密度
    mask2 = (d > t1) & (d <= t2)
    d_stretched[mask2] = r2 * (d[mask2] - t1) + y1

    # 高密度
    d_stretched[d > t2] = 1.0
    return d_stretched

def generate_density(point_cloud, max_coords, min_coords, width=img_size, height=img_size):
    """根据投影点生成 2D 密度图。

    参数:
        point_cloud: 形状为 (N,2) 的二维点云坐标。
        max_coords/min_coords: 原始坐标系下的边界，用于归一化到像素坐标。
        width/height: 目标密度图尺寸。
    返回:
        密度图 (float32, 0~1) 以及归一化所需字典。
    """

    ps = point_cloud
    image_res = np.array((width, height))

    normalization_dict = {}
    normalization_dict["min_coords"] = min_coords
    normalization_dict["max_coords"] = max_coords
    normalization_dict["image_res"] = image_res

    # 下面这个np.round()会将坐标四舍五入到整数，这是矢量化过程中导致精度截断的根本原因（因为矢量化过程中，坐标是浮点数，而密度图是整数）
    coordinates = \
        np.round(
            (ps[:, :2] - min_coords) / (max_coords - min_coords) * image_res)
    coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                image_res - 1)

    density = np.zeros((height, width), dtype=np.float32)

    unique_coordinates, counts = np.unique(coordinates, return_counts=True, axis=0)

    unique_coordinates = unique_coordinates.astype(np.int32)

    density[unique_coordinates[:, 1], unique_coordinates[:, 0]] = counts
    density = density / np.max(density)
    # ---------- 在 generate_density() 里 ----------
    # density = piecewise_stretch(density)  # 自适应阈值
    # density = (density * 255).astype(np.uint8)

    # 非线性映射，调整 density 值
    # mask_mid = (density >= 0.01) & (density <= 0.04)
    # mask_high = density > 0.04
    # density[mask_mid] *= 0
    # density[mask_high] = 1.0

    # mask_mid = (density >= 0.001) & (density <= 0.004)
    # mask_high = density > 0.004
    # density[mask_mid] *= 0
    # density[mask_high] = 1.0

    mask_mid = (density >= 0.001) & (density <= 0.04)
    mask_high = density > 0.04
    density[mask_mid] *= 25
    density[mask_high] = 1.0

    return density.astype(np.float32), normalization_dict


def generate_density_optimized(point_cloud, max_coords, min_coords, width=img_size, height=img_size):
    """优化版本的密度图生成函数。

    主要优化：
    1. 使用 np.histogramdd 替代 np.unique 提高性能
    2. 预先过滤边界外的点
    3. 减少内存分配

    参数:
        point_cloud: 形状为 (N,2) 的二维点云坐标。
        max_coords/min_coords: 原始坐标系下的边界，用于归一化到像素坐标。
        width/height: 目标密度图尺寸。
    返回:
        密度图 (float32, 0~1) 以及归一化所需字典。
    """
    if point_cloud.shape[0] == 0:
        return np.zeros((height, width), dtype=np.float32), {
            "min_coords": min_coords,
            "max_coords": max_coords,
            "image_res": np.array((width, height))
        }

    ps = point_cloud
    image_res = np.array((width, height))

    normalization_dict = {
        "min_coords": min_coords,
        "max_coords": max_coords,
        "image_res": image_res
    }

    # 归一化坐标到 [0, image_res-1] 范围
    normalized_coords = (ps[:, :2] - min_coords) / (max_coords - min_coords) * image_res

    # 预先过滤边界外的点
    valid_mask = (
        (normalized_coords[:, 0] >= 0) & (normalized_coords[:, 0] < width) &
        (normalized_coords[:, 1] >= 0) & (normalized_coords[:, 1] < height)
    )

    if not np.any(valid_mask):
        return np.zeros((height, width), dtype=np.float32), normalization_dict

    valid_coords = normalized_coords[valid_mask]

    # 使用 histogram2d 生成密度图，比 unique + 循环赋值更快
    density, _, _ = np.histogram2d(
        valid_coords[:, 1], valid_coords[:, 0],  # 注意 y, x 顺序
        bins=[height, width],
        range=[[0, height], [0, width]]
    )

    # 归一化
    max_density = np.max(density)
    if max_density > 0:
        density = density / max_density

    # 应用非线性映射
    mask_mid = (density >= 0.001) & (density <= 0.04)
    mask_high = density > 0.04
    density[mask_mid] *= 25
    density[mask_high] = 1.0

    return density.astype(np.float32), normalization_dict

def normalize_point(point, normalization_dict):

    min_coords = normalization_dict["min_coords"]
    max_coords = normalization_dict["max_coords"]
    image_res = normalization_dict["image_res"]

    point_2d = \
        np.round(
            (point[:2] - min_coords[:2]) / (max_coords[:2] - min_coords[:2]) * image_res)
    point_2d = np.minimum(np.maximum(point_2d, np.zeros_like(image_res)),
                            image_res - 1)

    point[:2] = point_2d.tolist()

    return point

def normalize_annotations(scene_path, normalization_dict):
    annotation_path = os.path.join(scene_path, "annotation_3d.json")
    with open(annotation_path, "r") as f:
        annotation_json = json.load(f)

    for line in annotation_json["lines"]:
        point = line["point"]
        point = normalize_point(point, normalization_dict)
        line["point"] = point

    for junction in annotation_json["junctions"]:
        point = junction["coordinate"]
        point = normalize_point(point, normalization_dict)
        junction["coordinate"] = point

    return annotation_json

def parse_floor_plan_polys(annos, max_coords, min_coords, image_res=np.array((img_size, img_size))):

    # construct each polygon
    polygons = []
    for frame in annos['frames']:
        for instance in frame['instances']:

            if instance['category'] != 'room':
                continue

            for edge in instance['edge']:
                vertices_x = np.array(edge['vertices']['x'], dtype=np.int32)
                vertices_y = np.array(edge['vertices']['y'], dtype=np.int32)
                ps = np.column_stack((vertices_x, vertices_y))
                polygon = ps[:-1, :]

            polygons.append([polygon, instance['category']])

    return polygons

def convert_lines_to_vertices(lines):
    """
    convert line representation to polygon vertices

    """
    polygons = []
    lines = np.array(lines)

    polygon = None
    while len(lines) != 0:
        if polygon is None:
            polygon = lines[0].tolist()
            lines = np.delete(lines, 0, 0)

        lineID, juncID = np.where(lines == polygon[-1])
        vertex = lines[lineID[0], 1 - juncID[0]]
        lines = np.delete(lines, lineID, 0)

        if vertex in polygon:
            polygons.append(polygon)
            polygon = None
        else:
            polygon.append(vertex)

    return polygons


def generate_coco_dict(annos, polygons, curr_instance_id, curr_img_id, ignore_types, scene_name: str = None):

    coco_annotation_dict_list = []

    for poly_ind, (polygon, poly_type) in enumerate(polygons):
        if poly_type in ignore_types:
            continue

        # 多边形至少需要 4 个坐标点，否则认为无效
        if len(polygon) < 4:
            warn_msg = f"{scene_name or '未知场景'} | poly_idx={poly_ind} | type={poly_type} | vertices={len(polygon)}"
            print(f"警告：跳过无效多边形 {poly_ind} (类型: {poly_type})，坐标点不足(需要≥4，实际{len(polygon)})")
            # 记录到 checklist
            append_to_checklist('无效多边形', warn_msg)
            continue

        poly_shapely = Polygon(polygon)
        area = poly_shapely.area

        # assert area > 10
        # if area < 100:
        # —— 小面积多边形过滤 ——
        if poly_type not in ['door', 'window'] and area < 100:
            warn_msg = f"{scene_name or '未知场景'} | 小面积({area:.1f}) | poly_idx={poly_ind} | type={poly_type}"
            append_to_checklist('小面积多边形', warn_msg)
            continue
        if poly_type in ['door', 'window'] and area < 1:
            warn_msg = f"{scene_name or '未知场景'} | 小面积({area:.1f}) | poly_idx={poly_ind} | type={poly_type}"
            append_to_checklist('小面积多边形', warn_msg)
            continue

        rectangle_shapely = poly_shapely.envelope

        ### here we convert door/window annotation into a single line
        if poly_type in ['door', 'window']:
            assert polygon.shape[0] == 4
            midp_1 = (polygon[0] + polygon[1])/2
            midp_2 = (polygon[1] + polygon[2])/2
            midp_3 = (polygon[2] + polygon[3])/2
            midp_4 = (polygon[3] + polygon[0])/2

            dist_1_3 = np.square(midp_1 -midp_3).sum()
            dist_2_4 = np.square(midp_2 -midp_4).sum()
            if dist_1_3 > dist_2_4:
                polygon = np.row_stack([midp_1, midp_3])
            else:
                polygon = np.row_stack([midp_2, midp_4])

        coco_seg_poly = []
        poly_sorted = resort_corners(polygon)

        for p in poly_sorted:
            coco_seg_poly += list(p)

        # Slightly wider bounding box
        bound_pad = 2
        bb_x, bb_y = rectangle_shapely.exterior.xy
        bb_x = np.unique(bb_x)
        bb_y = np.unique(bb_y)
        bb_x_min = np.maximum(np.min(bb_x) - bound_pad, 0)
        bb_y_min = np.maximum(np.min(bb_y) - bound_pad, 0)

        bb_x_max = np.minimum(np.max(bb_x) + bound_pad, img_size - 1)
        bb_y_max = np.minimum(np.max(bb_y) + bound_pad, img_size - 1)

        bb_width = (bb_x_max - bb_x_min)
        bb_height = (bb_y_max - bb_y_min)

        coco_bb = [bb_x_min, bb_y_min, bb_width, bb_height]

        coco_annotation_dict = {
                "segmentation": [coco_seg_poly],
                "area": area,
                "iscrowd": 0,
                "image_id": curr_img_id,
                "bbox": coco_bb,
                "category_id": type2id[poly_type],
                "id": curr_instance_id}
        
        coco_annotation_dict_list.append(coco_annotation_dict)
        curr_instance_id += 1


    return coco_annotation_dict_list


def calculate_slopes(vectorized_gt, type='mean'):
    """
    tpye: 'greater' or 'less' or 'mean'
    计算斜率并返回斜率均值和调整后的角度。
    """
    slopes_data_type = 'longest' # 'longest' or 'all'

    slopes_vec = []
    all_x, all_y = [], []
    for frame in vectorized_gt['frames']:
        for instance in frame['instances']:

            for edge in instance['edge']:
                vertices_x = np.array(edge['vertices']['x'], dtype=np.float32)
                vertices_y = np.array(edge['vertices']['y'], dtype=np.float32)
                ps = np.column_stack((vertices_x, vertices_y))
                all_x.extend(edge['vertices']['x'])
                all_y.extend(edge['vertices']['y'])

                if slopes_data_type == 'longest':
                    # 计算相邻点的欧式距离
                    distances = np.sqrt(np.sum((ps[:-1] - ps[1:])**2, axis=1))

                    # 找到最长的间隔点对
                    max_distance_index = np.argmax(distances)
                    slopes = (ps[max_distance_index + 1, 1] - ps[max_distance_index, 1]) / \
                        (ps[max_distance_index + 1, 0] - ps[max_distance_index, 0])
                    slopes_vec.append(slopes)
                else:
                    slopes = (ps[1:, 1] - ps[:-1, 1]) / \
                        (ps[1:, 0] - ps[:-1, 0])
                    slopes_vec.extend(slopes)

    all_x = np.array(all_x)
    all_y = np.array(all_y)
    vertices_xy = np.column_stack((all_x, all_y))

    if vertices_xy.shape[0] == 0:
        return 0, np.array([0, 0]), np.array([0, 0])

    max_coords = np.max(vertices_xy, axis=0)
    min_coords = np.min(vertices_xy, axis=0)
    max_m_min = max_coords - min_coords
    max_coords = max_coords + 0.1 * max_m_min
    min_coords = min_coords - 0.1 * max_m_min

    if not slopes_vec:
        return 0, max_coords, min_coords

    # mean_slope = np.mean(slopes_vec)
    # slopes_greater_than_mean = [slope for slope in slopes_vec if slope > mean_slope]
    # slopes_less_than_mean = [slope for slope in slopes_vec if slope <= mean_slope]

    # mean_slope_greater = np.mean(slopes_greater_than_mean) if slopes_greater_than_mean else 0
    # mean_slope_less = np.mean(slopes_less_than_mean) if slopes_less_than_mean else 0

    # if np.abs(mean_slope_greater) > np.abs(mean_slope_less):
    #     final_slope = mean_slope_less
    # else:
    #     final_slope = mean_slope_greater

    filtered_slopes_vec = [slope for slope in slopes_vec if -1 < slope < 1]
    final_slope = np.mean(filtered_slopes_vec)

    slope_angle = np.arctan(final_slope)

    # angle_greater = np.arctan(mean_slope_greater)
    # angle_less = np.arctan(mean_slope_less)
    # adjusted_angle_greater = np.pi / 2 - angle_greater
    # mean_adjusted_angle = np.mean([adjusted_angle_greater, angle_less])

    # if type == 'greater':
    #     slope_angle = angle_greater
    # elif type == 'less':
    #     slope_angle = angle_less
    # else:    
    #     slope_angle = mean_adjusted_angle
    
    return slope_angle, max_coords, min_coords

def rot_normalize_vectorized_gt(vectorized_gt, slope, max_coords, min_coords, image_res):
    """
    对矢量化真值进行旋转并存储图片。
    """
    rot_image = np.zeros((image_res[1], image_res[0], 3), dtype=np.uint8)
    for frame in vectorized_gt['frames']:
        for instance in frame['instances']:

            if instance['category'] != 'room' and instance['category'] != 'door':
                continue

            for edge in instance['edge']:
                vertices_x = np.array(edge['vertices']['x'], dtype=np.float32)
                vertices_y = np.array(edge['vertices']['y'], dtype=np.float32)
                ps = np.column_stack((vertices_x, vertices_y))

                rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope), 1)
                rotated_ps = np.dot(
                    np.column_stack((ps, np.ones(ps.shape[0]))), rotation_matrix.T
                )[:, :2]

                coordinates = np.round(
                    (rotated_ps - min_coords) / (max_coords - min_coords) * image_res
                )
                coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                          image_res - 1).astype(np.int32)
                
                edge['vertices']['x'] = coordinates[:, 0].tolist()
                edge['vertices']['y'] = coordinates[:, 1].tolist()

                cv2.polylines(rot_image, [coordinates], False, (255, 255, 255), 1)

    return vectorized_gt, rot_image

# 转换 numpy 数据类型为原生 Python 数据类型
def convert_numpy_to_native(obj):
    if isinstance(obj, np.ndarray):
        return obj.tolist()  # 转换为列表
    elif isinstance(obj, (np.int32, np.int64)):
        return int(obj)  # 转换为 Python int
    elif isinstance(obj, (np.float32, np.float64)):
        return float(obj)  # 转换为 Python float
    else:
        raise TypeError(f"Type {type(obj)} not serializable")
