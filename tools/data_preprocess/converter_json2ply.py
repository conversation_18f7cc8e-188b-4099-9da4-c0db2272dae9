#!/usr/bin/env python3
"""
JSON to PLY Point Cloud Converter

This script converts indoor point cloud door/window detection dataset from JSON annotation format
to PLY point cloud format for visualization and analysis.

Author: Bo Qiu
Date: 2024
"""

import json
import numpy as np
import os
import glob
import argparse
import laspy
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from scipy.spatial import KDTree
from multiprocessing import Pool, cpu_count
from functools import partial

# Import existing utilities - will be imported when needed to avoid initialization issues


class JSONToPLYConverter:
    """
    Main converter class for transforming JSON annotation data to PLY point cloud format.
    
    This class handles the complete pipeline:
    1. Reading JSON annotation files
    2. Loading RGB and intensity data from LAS files
    3. Filtering points based on room bounding boxes
    4. Saving processed data as PLY files
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the converter with configuration parameters.
        
        Args:
            config: Configuration dictionary containing paths and parameters
        """
        self.config = config
        self.logger = self._setup_logging()
        # Note: We use internal filename conversion method instead of external FilenameConverter
        
        # Data paths
        self.json_data_root = config['json_data_root']
        self.rgb_data_root = config['rgb_data_root']
        self.floorplan_data_root = config['floorplan_data_root']
        self.output_root = config['output_root']

        # Batch configuration
        self.batch_name_list = config.get('batch_name_list', [
            "RS10_Batch_01"
            "RS10_Batch_02",
            "RS10_Batch_03",
            "RS10_Batch_04",
            "RS10_Batch_05"
        ])
        
        # Processing parameters
        self.tolerance = config.get('tolerance', 1e-6)
        self.num_workers = min(config.get('num_workers', 4), 96)  # Limit to 64 workers max
        self.batch_size = config.get('batch_size', 1)
        self.max_scenes = config.get('max_scenes', None)
        
        # Instance label parameters
        self.target_categories = config.get('target_categories', ['door', 'window', 'openings'])
        self.background_label = config.get('background_label', 0)

        # RGB and intensity control parameters
        self.save_rgb = config.get('save_rgb', True)
        self.save_intensity = config.get('save_intensity', True)
        
        # Statistics
        self.stats = {
            'total_scenes': 0,
            'processed_scenes': 0,
            'failed_scenes': 0,
            'total_points': 0,
            'filtered_points': 0
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging system with file and console handlers."""
        # Create log directory
        log_dir = Path("work_dirs/data_preprocess_log")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate log filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = log_dir / f"converter_json2ply_log_{timestamp}.log"
        
        # Configure logger
        logger = logging.getLogger('JSONToPLYConverter')
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler (with error handling)
        try:
            file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
            file_formatter = logging.Formatter(
                '%(asctime)s - %(process)d - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        except (PermissionError, OSError) as e:
            print(f"Warning: Could not create log file {log_file}: {e}")
            print("Continuing with console logging only...")
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        return logger


    
    def load_scene_names(self) -> List[str]:
        """
        Load scene names from batch directories, similar to process2_vis_las.py.

        Returns:
            List of scene names to process
        """
        all_scene_names = []

        for batch_name in self.batch_name_list:
            self.logger.info(f"Scanning batch: {batch_name}")

            # Construct batch directory path
            batch_dir = os.path.join(self.json_data_root, batch_name)

            if not os.path.exists(batch_dir):
                self.logger.warning(f"Batch directory not found: {batch_dir}")
                continue

            # Get all scene directories in this batch
            try:
                scene_dirs = sorted([
                    f for f in os.listdir(batch_dir)
                    if os.path.isdir(os.path.join(batch_dir, f))
                ])

                self.logger.info(f"Found {len(scene_dirs)} scenes in {batch_name}")

                # Add batch info to scene names for processing
                for scene_dir in scene_dirs:
                    scene_info = {
                        'scene_name': scene_dir,
                        'batch_name': batch_name
                    }
                    all_scene_names.append(scene_info)

            except Exception as e:
                self.logger.error(f"Error scanning batch {batch_name}: {e}")
                continue

        self.logger.info(f"Total scenes found: {len(all_scene_names)}")

        # Apply max_scenes limit if specified
        if self.max_scenes and self.max_scenes > 0:
            all_scene_names = all_scene_names[:self.max_scenes]
            self.logger.info(f"Limited to first {len(all_scene_names)} scenes due to max_scenes setting")

        return all_scene_names
    
    def load_json_annotations(self, scene_info: Dict) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Load and combine all JSON annotation files for a scene.

        Args:
            scene_info: Dictionary containing scene_name and batch_name

        Returns:
            Tuple of (coordinates, semantic_labels, instance_labels)
        """
        scene_name = scene_info['scene_name']
        batch_name = scene_info['batch_name']
        json_dir = os.path.join(self.json_data_root, batch_name, scene_name, 'pcd')
        
        if not os.path.exists(json_dir):
            self.logger.warning(f"JSON directory not found: {json_dir}")
            return np.empty((0, 3)), np.empty((0,)), np.empty((0,))
        
        json_files = sorted(glob.glob(os.path.join(json_dir, "*.json")))
        if not json_files:
            self.logger.warning(f"No JSON files found in: {json_dir}")
            return np.empty((0, 3)), np.empty((0,)), np.empty((0,))
        
        all_coords = []
        all_semantic = []
        all_instance = []
        
        for i, json_path in enumerate(json_files):
            try:
                coords, semantic, instance = self._load_single_json(json_path, i)
                if len(coords) > 0:
                    all_coords.append(coords)
                    all_semantic.append(semantic)
                    all_instance.append(instance)
                    
            except Exception as e:
                self.logger.error(f"Failed to load JSON file {json_path}: {e}")
                continue
        
        if not all_coords:
            return np.empty((0, 3)), np.empty((0,)), np.empty((0,))
        
        # Combine all data
        combined_coords = np.vstack(all_coords)
        combined_semantic = np.concatenate(all_semantic)
        combined_instance = np.concatenate(all_instance)
        
        self.logger.info(f"Loaded {len(combined_coords)} points from {len(json_files)} JSON files")
        return combined_coords, combined_semantic, combined_instance
    
    def _load_single_json(self, json_path: str, file_index: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Load a single JSON annotation file.
        
        Args:
            json_path: Path to JSON file
            file_index: Index of the JSON file for instance label offset
            
        Returns:
            Tuple of (coordinates, semantic_labels, instance_labels)
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'frames' not in data:
            raise ValueError("Missing 'frames' object in JSON")
        
        json_info = data['frames'][0]
        required_keys = {'cameraInfoList', 'frameInfo', 'instances'}
        if not required_keys.issubset(json_info.keys()):
            missing = required_keys - json_info.keys()
            raise ValueError(f"Missing required keys: {missing}")
        
        coords_list = []
        semantic_list = []
        instance_list = []
        
        # Process each instance
        for instance in json_info['instances']:
            category = instance['category']
            instance_id = instance.get('id', 0)

            # Ensure instance_id is an integer
            try:
                if isinstance(instance_id, str):
                    instance_id = int(instance_id)
                elif not isinstance(instance_id, int):
                    instance_id = 0
            except (ValueError, TypeError):
                instance_id = 0

            # Process instance label
            if category in self.target_categories:
                # Use offset formula: file_index * 1000 + original_instance_id
                processed_instance_id = file_index * 1000 + instance_id
            else:
                # Set as background
                processed_instance_id = self.background_label
            
            # Process shapes
            for shape in instance.get('shapes', []):
                size = shape.get('size', 0)

                # Ensure size is an integer
                try:
                    if isinstance(size, str):
                        size = int(size)
                    elif not isinstance(size, int):
                        size = 0
                except (ValueError, TypeError):
                    size = 0

                if size <= 0:
                    continue

                shape_data = shape.get('shapeData', {})
                
                if isinstance(shape_data, dict):
                    x_data = shape_data.get('x', [])
                    y_data = shape_data.get('y', [])
                    z_data = shape_data.get('z', [])

                    if len(x_data) == len(y_data) == len(z_data) == size:
                        try:
                            # Ensure coordinate data are numeric
                            x_data = np.array(x_data, dtype=np.float32)
                            y_data = np.array(y_data, dtype=np.float32)
                            z_data = np.array(z_data, dtype=np.float32)

                            coords = np.stack([x_data, y_data, z_data], axis=1)
                            semantic_labels = np.full(size, self._get_semantic_label(category))
                            instance_labels = np.full(size, processed_instance_id)

                            coords_list.append(coords)
                            semantic_list.append(semantic_labels)
                            instance_list.append(instance_labels)
                        except (ValueError, TypeError) as e:
                            self.logger.warning(f"Failed to convert coordinate data to numeric: {e}")
                            continue
        
        if not coords_list:
            return np.empty((0, 3)), np.empty((0,)), np.empty((0,))
        
        return (np.vstack(coords_list), 
                np.concatenate(semantic_list), 
                np.concatenate(instance_list))
    
    def _get_semantic_label(self, category: str) -> int:
        """
        Convert category string to semantic label ID.
        
        Args:
            category: Category name
            
        Returns:
            Semantic label ID
        """
        # Define semantic mapping (can be made configurable)
        semantic_mapping = {
            'wall':              0,
            'floor':             1,
            'ceiling':           2,
            'door':              3,
            'window':            4,
            'openings':          5,  # Map to door
            'suspended-ceiling': 6,  # Map to ceiling
            'noise':             7,
            'not_care':          8
        }
        
        return semantic_mapping.get(category, 8)  # Default to 'other' class

        # # Define semantic mapping (can be made configurable)
        # semantic_mapping = {
        #     'wall': 0,
        #     'floor': 1,
        #     'ceiling': 2,
        #     'door': 3,
        #     'window': 4,
        #     'openings': 3,  # Map to door
        #     'suspended-ceiling': 2,  # Map to ceiling
        #     'noise': 5,
        #     'not_care': 5
        # }
        
        # return semantic_mapping.get(category, 5)  # Default to 'other' class

    def _create_dummy_attributes(self, num_points: int) -> Dict[str, np.ndarray]:
        """
        Create dummy RGB and intensity attributes when LAS data is not available.

        Args:
            num_points: Number of points

        Returns:
            Dictionary with dummy attributes
        """
        return {
            'red': np.full(num_points, 128, dtype=np.uint8),
            'green': np.full(num_points, 128, dtype=np.uint8),
            'blue': np.full(num_points, 128, dtype=np.uint8),
            'intensity': np.full(num_points, 0.0, dtype=np.float32)
        }

    def _create_dummy_rgb(self, num_points: int) -> np.ndarray:
        """
        Create dummy RGB data.

        Args:
            num_points: Number of points

        Returns:
            RGB array with default gray color
        """
        return np.full((num_points, 3), 128, dtype=np.uint8)

    def _create_dummy_intensity(self, num_points: int) -> np.ndarray:
        """
        Create dummy intensity data.

        Args:
            num_points: Number of points

        Returns:
            Intensity array with default zero values
        """
        return np.zeros(num_points, dtype=np.float32)

    def _convert_new_to_old_filename(self, new_filename: str) -> str:
        """
        Convert filename from new format to old format.

        Args:
            new_filename: Filename in new format (e.g., "000_biguiyuanxingzuan_res-ff_RS10")

        Returns:
            Filename in old format (e.g., "biguiyuanxingzuan_res_ff_RS10_0")
        """
        try:
            # Parse new format: {house_code_3digits}_{community_name}_{property-with-hyphens}_{device_name}
            parts = new_filename.split('_')
            if len(parts) < 4:
                self.logger.warning(f"Invalid new filename format: {new_filename}")
                return new_filename

            house_code = parts[0]  # e.g., "000"
            community_name = parts[1]  # e.g., "biguiyuanxingzuan"
            property_part = parts[2]  # e.g., "res-ff"
            device_name = parts[3]  # e.g., "RS10"

            # Convert property part: "res-ff" -> "res_ff"
            property_converted = property_part.replace('-', '_')

            # Remove leading zeros from house code
            house_code_int = int(house_code)

            # Build old format: {community_name}_{property_converted}_{device_name}_{house_code}
            old_filename = f"{community_name}_{property_converted}_{device_name}_{house_code_int}"

            return old_filename

        except Exception as e:
            self.logger.error(f"Failed to convert filename {new_filename}: {e}")
            return new_filename
    
    def load_rgb_intensity_data(self, scene_info: Dict) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """
        Load RGB and intensity data from LAS file.

        Args:
            scene_info: Dictionary containing scene_name and batch_name

        Returns:
            Tuple of (coordinates, attributes_dict)
        """
        scene_name = scene_info['scene_name']
        batch_name = scene_info['batch_name']

        # Correct RGB data path structure: /rgb_data_root/RS10/batch_name/scene_name/Las
        rgb_dir = os.path.join(self.rgb_data_root, 'RS10', batch_name, scene_name, 'Las')
        
        if not os.path.exists(rgb_dir):
            self.logger.warning(f"RGB directory not found: {rgb_dir}")
            return np.empty((0, 3)), {}
        
        las_files = glob.glob(os.path.join(rgb_dir, "*.las"))
        if len(las_files) != 1:
            self.logger.warning(f"Expected 1 LAS file, found {len(las_files)}: {las_files}")
            return np.empty((0, 3)), {}
        
        las_path = las_files[0]
        
        try:
            return self._load_las_file(las_path)
        except Exception as e:
            self.logger.error(f"Failed to load LAS file {las_path}: {e}")
            return np.empty((0, 3)), {}
    
    def _load_las_file(self, las_path: str) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """
        Load a single LAS file and extract coordinates and attributes.
        
        Args:
            las_path: Path to LAS file
            
        Returns:
            Tuple of (coordinates, attributes_dict)
        """
        las = laspy.read(las_path)
        
        # Extract coordinates
        coords = np.vstack((las.x, las.y, las.z)).transpose()
        
        # Initialize attributes
        attributes = {
            'intensity': las.intensity,
            'red': np.ones_like(las.x, dtype=np.uint8) * 128,
            'green': np.ones_like(las.y, dtype=np.uint8) * 128,
            'blue': np.ones_like(las.z, dtype=np.uint8) * 128
        }
        
        # Process RGB if available
        if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):
            red, green, blue = las.red, las.green, las.blue
            
            # Check if normalization is needed
            rgb_max = max(red.max(), green.max(), blue.max())
            if rgb_max > 255:
                # Normalize from 16-bit to 8-bit
                attributes['red'] = (red / 65535.0 * 255).astype(np.uint8)
                attributes['green'] = (green / 65535.0 * 255).astype(np.uint8)
                attributes['blue'] = (blue / 65535.0 * 255).astype(np.uint8)
            else:
                attributes['red'] = red.astype(np.uint8)
                attributes['green'] = green.astype(np.uint8)
                attributes['blue'] = blue.astype(np.uint8)
        
        return coords, attributes

    def match_points_with_attributes(self, json_coords: np.ndarray, semantic_labels: np.ndarray,
                                   instance_labels: np.ndarray, las_coords: np.ndarray,
                                   attributes: Dict[str, np.ndarray]) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Match JSON points with LAS attributes using KDTree.

        Args:
            json_coords: JSON point coordinates
            semantic_labels: Semantic labels for JSON points
            instance_labels: Instance labels for JSON points
            las_coords: LAS point coordinates
            attributes: LAS point attributes

        Returns:
            Tuple of (matched_coords, matched_rgb, matched_intensity, matched_semantic, matched_instance)
        """
        if len(json_coords) == 0 or len(las_coords) == 0:
            return (np.empty((0, 3)), np.empty((0, 3)), np.empty((0,)),
                   np.empty((0,)), np.empty((0,)))

        # Build KDTree for efficient nearest neighbor search
        tree = KDTree(las_coords)

        # Query nearest neighbors
        distances, indices = tree.query(json_coords, k=1, workers=self.num_workers)

        # Filter points within tolerance
        mask = distances < self.tolerance
        matched_indices = np.where(mask)[0]
        matched_las_indices = indices[matched_indices]

        if len(matched_indices) == 0:
            self.logger.warning("No points matched within tolerance")
            return (np.empty((0, 3)), np.empty((0, 3)), np.empty((0,)),
                   np.empty((0,)), np.empty((0,)))

        # Extract matched data
        matched_coords = json_coords[matched_indices]
        matched_rgb = np.column_stack([
            attributes['red'][matched_las_indices],
            attributes['green'][matched_las_indices],
            attributes['blue'][matched_las_indices]
        ])
        matched_intensity = attributes['intensity'][matched_las_indices]
        matched_semantic = semantic_labels[matched_indices]
        matched_instance = instance_labels[matched_indices]

        self.logger.info(f"Matched {len(matched_coords)}/{len(json_coords)} points within tolerance {self.tolerance}")
        return matched_coords, matched_rgb, matched_intensity, matched_semantic, matched_instance

    def load_room_bounding_box(self, scene_info: Dict) -> Optional[List[float]]:
        """
        Load room bounding box from floorplan.json file.

        Args:
            scene_info: Dictionary containing scene_name and batch_name

        Returns:
            Bounding box as [min_x, min_y, min_z, max_x, max_y, max_z] or None
        """
        scene_name = scene_info['scene_name']

        try:
            # Convert filename from new format to old format
            # Note: We need to reverse the conversion logic since we're going from new to old format
            old_scene_name = self._convert_new_to_old_filename(scene_name)

            # Construct floorplan.json path
            floorplan_path = os.path.join(self.floorplan_data_root, old_scene_name, 'Annotations', 'floorplan.json')

            if not os.path.exists(floorplan_path):
                self.logger.warning(f"Floorplan file not found: {floorplan_path}")
                return None

            # Load floorplan data
            with open(floorplan_path, 'r', encoding='utf-8') as f:
                floorplan_data = json.load(f)

            # Extract bounding box
            if 'frames' in floorplan_data and len(floorplan_data['frames']) > 0:
                bounding_box = floorplan_data['frames'][0].get('boundingBox')
                if bounding_box and len(bounding_box) == 6:
                    self.logger.info(f"Loaded bounding box for {scene_name}: {bounding_box}")
                    return bounding_box

            self.logger.warning(f"Invalid bounding box in floorplan: {floorplan_path}")
            return None

        except Exception as e:
            self.logger.error(f"Failed to load bounding box for {scene_name}: {e}")
            return None

    def filter_points_by_bounding_box(self, coords: np.ndarray, rgb: np.ndarray,
                                    intensity: np.ndarray, semantic: np.ndarray,
                                    instance: np.ndarray, bounding_box: List[float]) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        Filter points based on room bounding box.

        Args:
            coords: Point coordinates
            rgb: RGB colors
            intensity: Intensity values
            semantic: Semantic labels
            instance: Instance labels
            bounding_box: [min_x, min_y, min_z, max_x, max_y, max_z]

        Returns:
            Filtered data tuple
        """
        if len(coords) == 0:
            return coords, rgb, intensity, semantic, instance

        min_x, min_y, min_z, max_x, max_y, max_z = bounding_box

        # Create filter mask
        mask = (
            (coords[:, 0] >= min_x) & (coords[:, 0] <= max_x) &
            (coords[:, 1] >= min_y) & (coords[:, 1] <= max_y) &
            (coords[:, 2] >= min_z) & (coords[:, 2] <= max_z)
        )

        # Apply filter
        filtered_coords = coords[mask]
        filtered_rgb = rgb[mask]
        filtered_intensity = intensity[mask]
        filtered_semantic = semantic[mask]
        filtered_instance = instance[mask]

        self.logger.info(f"Filtered {len(filtered_coords)}/{len(coords)} points within bounding box")
        return filtered_coords, filtered_rgb, filtered_intensity, filtered_semantic, filtered_instance

    def save_ply_file(self, output_path: str, coords: np.ndarray, rgb: np.ndarray,
                     intensity: np.ndarray, semantic: np.ndarray, instance: np.ndarray) -> bool:
        """
        Save point cloud data to PLY format file.

        Args:
            output_path: Output PLY file path
            coords: Point coordinates (N, 3)
            rgb: RGB colors (N, 3)
            intensity: Intensity values (N,)
            semantic: Semantic labels (N,)
            instance: Instance labels (N,)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Validate input data
            n_points = len(coords)
            if n_points == 0:
                self.logger.warning(f"No points to save for {output_path}")
                return False

            if not (len(rgb) == len(intensity) == len(semantic) == len(instance) == n_points):
                self.logger.error(f"Data length mismatch for {output_path}")
                return False

            # Write PLY file
            with open(output_path, 'w') as f:
                # Write PLY header
                f.write("ply\n")
                f.write("format ascii 1.0\n")
                f.write(f"element vertex {n_points}\n")
                f.write("property float x\n")
                f.write("property float y\n")
                f.write("property float z\n")

                # Add RGB properties if enabled
                if self.save_rgb:
                    f.write("property uchar red\n")
                    f.write("property uchar green\n")
                    f.write("property uchar blue\n")

                # Add intensity property if enabled
                if self.save_intensity:
                    f.write("property float intensity\n")

                f.write("property int semantic_label\n")
                f.write("property int instance_label\n")
                f.write("end_header\n")

                # Prepare data for writing based on enabled features
                data_columns = [coords.astype(np.float32)]
                format_strings = ['%.6f', '%.6f', '%.6f']

                if self.save_rgb:
                    data_columns.append(rgb.astype(np.uint8))
                    format_strings.extend(['%d', '%d', '%d'])

                if self.save_intensity:
                    data_columns.append(intensity.astype(np.float32))
                    format_strings.append('%.6f')

                data_columns.extend([
                    semantic.astype(np.int32),
                    instance.astype(np.int32)
                ])
                format_strings.extend(['%d', '%d'])

                data_to_write = np.column_stack(data_columns)

                # Write data using numpy's efficient savetxt
                np.savetxt(f, data_to_write, fmt=format_strings)

            self.logger.info(f"Successfully saved PLY file: {output_path} ({n_points} points)")
            return True

        except Exception as e:
            self.logger.error(f"Failed to save PLY file {output_path}: {e}")
            return False

    def process_single_scene(self, scene_info: Dict) -> bool:
        """
        Process a single scene from JSON to PLY.

        Args:
            scene_info: Dictionary containing scene_name and batch_name

        Returns:
            True if successful, False otherwise
        """
        scene_name = scene_info['scene_name']
        batch_name = scene_info['batch_name']

        try:
            self.logger.info(f"Processing scene: {scene_name} (batch: {batch_name})")
            start_time = time.time()

            # Step 1: Load JSON annotations
            json_coords, semantic_labels, instance_labels = self.load_json_annotations(scene_info)
            if len(json_coords) == 0:
                self.logger.warning(f"No JSON data found for scene: {scene_name}")
                return False

            # Step 2: Load RGB and intensity data (optional)
            if self.save_rgb or self.save_intensity:
                las_coords, attributes = self.load_rgb_intensity_data(scene_info)
                if len(las_coords) == 0:
                    self.logger.warning(f"No RGB/intensity data found for scene: {scene_name}")
                    if self.save_rgb and self.save_intensity:
                        self.logger.warning("Both RGB and intensity are required but not found, skipping scene")
                        return False
                    else:
                        self.logger.info("RGB/intensity data not found, proceeding without it")
                        # Create dummy data for processing
                        las_coords = json_coords.copy()
                        attributes = self._create_dummy_attributes(len(json_coords))

                # Step 3: Match points with attributes
                matched_coords, matched_rgb, matched_intensity, matched_semantic, matched_instance = \
                    self.match_points_with_attributes(json_coords, semantic_labels, instance_labels,
                                                    las_coords, attributes)

                if len(matched_coords) == 0:
                    self.logger.warning(f"No points matched for scene: {scene_name}")
                    return False
            else:
                # Skip RGB/intensity processing entirely
                self.logger.info("RGB and intensity processing skipped (save_rgb=False, save_intensity=False)")
                matched_coords = json_coords
                matched_semantic = semantic_labels
                matched_instance = instance_labels
                # Create dummy RGB and intensity data
                matched_rgb = self._create_dummy_rgb(len(matched_coords))
                matched_intensity = self._create_dummy_intensity(len(matched_coords))

            # Step 4: Load room bounding box and filter points
            bounding_box = self.load_room_bounding_box(scene_info)
            if bounding_box:
                matched_coords, matched_rgb, matched_intensity, matched_semantic, matched_instance = \
                    self.filter_points_by_bounding_box(matched_coords, matched_rgb, matched_intensity,
                                                     matched_semantic, matched_instance, bounding_box)
            else:
                self.logger.warning(f"No bounding box found for scene: {scene_name}, skipping filtering")

            if len(matched_coords) == 0:
                self.logger.warning(f"No points remaining after filtering for scene: {scene_name}")
                return False

            # Step 5: Save PLY file
            output_path = os.path.join(self.output_root, f"{scene_name}.ply")
            success = self.save_ply_file(output_path, matched_coords, matched_rgb, matched_intensity,
                                       matched_semantic, matched_instance)

            # Update statistics
            if success:
                self.stats['processed_scenes'] += 1
                self.stats['total_points'] += len(matched_coords)

                processing_time = time.time() - start_time
                self.logger.info(f"Successfully processed {scene_name} in {processing_time:.2f}s "
                               f"({len(matched_coords)} points)")
                return True
            else:
                self.stats['failed_scenes'] += 1
                return False

        except Exception as e:
            self.logger.error(f"Failed to process scene {scene_name}: {e}", exc_info=True)
            self.stats['failed_scenes'] += 1
            return False

    def process_all_scenes(self, scene_infos: List[Dict]) -> None:
        """
        Process all scenes with optional multiprocessing.

        Args:
            scene_infos: List of scene info dictionaries to process
        """
        self.stats['total_scenes'] = len(scene_infos)
        self.logger.info(f"Starting processing of {len(scene_infos)} scenes")
        self.logger.info(f"RGB processing: {'enabled' if self.save_rgb else 'disabled'}")
        self.logger.info(f"Intensity processing: {'enabled' if self.save_intensity else 'disabled'}")

        # Create output directory
        os.makedirs(self.output_root, exist_ok=True)

        if self.num_workers > 1 and len(scene_infos) > 1:
            # Multiprocessing mode
            self.logger.info(f"Using multiprocessing with {self.num_workers} workers")

            # Create worker function with fixed parameters
            worker_func = partial(self._process_scene_worker, self.config)

            # Use smaller chunk size to avoid memory issues
            chunk_size = max(1, len(scene_infos) // (self.num_workers * 4))

            with Pool(processes=self.num_workers) as pool:
                results = pool.map(worker_func, scene_infos, chunksize=chunk_size)

            # Aggregate results
            for success in results:
                if success:
                    self.stats['processed_scenes'] += 1
                else:
                    self.stats['failed_scenes'] += 1
        else:
            # Sequential processing
            self.logger.info("Using sequential processing")
            for scene_info in scene_infos:
                self.process_single_scene(scene_info)

        # Print final statistics
        self._print_final_statistics()

    @staticmethod
    def _process_scene_worker(config: Dict, scene_info: Dict) -> bool:
        """
        Worker function for multiprocessing.

        Args:
            config: Configuration dictionary
            scene_info: Scene info dictionary to process

        Returns:
            True if successful, False otherwise
        """
        # Create a new converter instance for this worker
        converter = JSONToPLYConverter(config)
        return converter.process_single_scene(scene_info)

    def _print_final_statistics(self) -> None:
        """Print final processing statistics."""
        self.logger.info("="*50)
        self.logger.info("PROCESSING COMPLETED")
        self.logger.info("="*50)
        self.logger.info(f"Total scenes: {self.stats['total_scenes']}")
        self.logger.info(f"Successfully processed: {self.stats['processed_scenes']}")
        self.logger.info(f"Failed: {self.stats['failed_scenes']}")
        self.logger.info(f"Success rate: {self.stats['processed_scenes']/max(1, self.stats['total_scenes'])*100:.1f}%")
        self.logger.info(f"Total points processed: {self.stats['total_points']}")
        self.logger.info("="*50)


def create_config_from_args(args) -> Dict:
    """
    Create configuration dictionary from command line arguments.

    Args:
        args: Parsed command line arguments

    Returns:
        Configuration dictionary
    """
    return {
        'json_data_root': args.json_data_root,
        'rgb_data_root': args.rgb_data_root,
        'floorplan_data_root': args.floorplan_data_root,
        'output_root': args.output_root,
        'batch_name_list': args.batch_name_list,
        'tolerance': args.tolerance,
        'num_workers': args.num_workers,
        'batch_size': args.batch_size,
        'target_categories': args.target_categories,
        'background_label': args.background_label,
        'max_scenes': args.max_scenes,
        'save_rgb': args.save_rgb,
        'save_intensity': args.save_intensity
    }


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Convert JSON annotation data to PLY point cloud format',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Input paths
    parser.add_argument(
        '--json_data_root',
        type=str,
        default='/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/',
        help='Root directory containing JSON annotation files'
    )

    parser.add_argument(
        '--rgb_data_root',
        type=str,
        default='/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/',
        help='Root directory containing RGB LAS files'
    )

    parser.add_argument(
        '--floorplan_data_root',
        type=str,
        default='/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        help='Root directory containing floorplan.json files'
    )

    # Batch configuration
    parser.add_argument(
        '--batch_name_list',
        nargs='+',
        default=["RS10_Batch_01", "RS10_Batch_02", "RS10_Batch_03", "RS10_Batch_04", "RS10_Batch_05"],
        help='List of batch names to process'
    )

    # Output path
    parser.add_argument(
        '--output_root',
        type=str,
        default='data/hc3d/raw_ply',
        help='Output directory for PLY files'
    )

    # Processing parameters
    parser.add_argument(
        '--tolerance',
        type=float,
        default=1e-6,
        help='Tolerance for point matching'
    )

    parser.add_argument(
        '--num_workers',
        type=int,
        default=64,
        help='Number of worker processes (max 64)'
    )

    parser.add_argument(
        '--batch_size',
        type=int,
        default=1,
        help='Batch size for processing'
    )

    parser.add_argument(
        '--target_categories',
        nargs='+',
        default=['door', 'window', 'openings'],
        help='Categories to extract instance labels for'
    )

    parser.add_argument(
        '--background_label',
        type=int,
        default=-1,
        help='Label value for background/other instances'
    )

    # Scene selection
    parser.add_argument(
        '--scene_names',
        nargs='*',
        help='Specific scene names to process (if not provided, process all)'
    )

    parser.add_argument(
        '--max_scenes',
        type=int,
        help='Maximum number of scenes to process (for testing, e.g., set to 1 for single scene test)'
    )

    # RGB and intensity control
    parser.add_argument(
        '--save_rgb',
        action='store_true',
        default=True,
        help='Save RGB color information to PLY file (default: True)'
    )

    parser.add_argument(
        '--no_rgb',
        action='store_false',
        dest='save_rgb',
        help='Do not save RGB color information (overrides --save_rgb)'
    )

    parser.add_argument(
        '--save_intensity',
        action='store_true',
        default=True,
        help='Save intensity information to PLY file (default: True)'
    )

    parser.add_argument(
        '--no_intensity',
        action='store_false',
        dest='save_intensity',
        help='Do not save intensity information (overrides --save_intensity)'
    )

    return parser.parse_args()


def main():
    """Main function."""
    # Parse command line arguments
    args = parse_arguments()

    # Create configuration
    config = create_config_from_args(args)

    # Create converter instance
    converter = JSONToPLYConverter(config)

    # Load scene information
    if args.scene_names:
        # Use specified scene names - need to find them in batches
        scene_infos = []
        for scene_name in args.scene_names:
            # Try to find the scene in available batches
            found = False
            for batch_name in config['batch_name_list']:
                batch_dir = os.path.join(config['json_data_root'], batch_name)
                scene_dir = os.path.join(batch_dir, scene_name)
                if os.path.exists(scene_dir):
                    scene_infos.append({'scene_name': scene_name, 'batch_name': batch_name})
                    found = True
                    break
            if not found:
                converter.logger.warning(f"Scene not found in any batch: {scene_name}")

        converter.logger.info(f"Processing specified scenes: {[s['scene_name'] for s in scene_infos]}")
    else:
        # Load all scene names from batch directories
        scene_infos = converter.load_scene_names()
        if not scene_infos:
            converter.logger.error("No scenes found to process")
            return 1

    # Validate input paths
    required_paths = [
        (config['json_data_root'], "JSON data root"),
        (config['rgb_data_root'], "RGB data root"),
        (config['floorplan_data_root'], "Floorplan data root")
    ]

    for path, name in required_paths:
        if not os.path.exists(path):
            converter.logger.error(f"{name} does not exist: {path}")
            return 1

    # Start processing
    try:
        converter.process_all_scenes(scene_infos)

        # Check if any scenes were processed successfully
        if converter.stats['processed_scenes'] > 0:
            converter.logger.info("Processing completed successfully")
            return 0
        else:
            converter.logger.error("No scenes were processed successfully")
            return 1

    except KeyboardInterrupt:
        converter.logger.info("Processing interrupted by user")
        return 1
    except Exception as e:
        converter.logger.error(f"Unexpected error during processing: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
