# UniDet3D 置信度阈值问题修复说明

## 🔍 问题分析

### 原始问题
用户发现即使将`--score-thr`设置为0.8，生成的.obj文件中仍然包含大量预测框，可视化效果不够清晰。

### 根本原因
UniDet3D中存在**两个不同的置信度阈值**，它们在不同阶段起作用：

1. **模型NMS阶段的阈值** (`test_cfg.score_thr`)
   - 位置：`configs/unidet3d_*.py`中的`model.test_cfg.score_thr`
   - 默认值：`0` (几乎不过滤)
   - 作用：在模型的NMS过程中过滤低置信度预测
   - 代码位置：`unidet3d/unidet3d.py`第734行

2. **可视化阶段的阈值** (`visualization_hook.score_thr`)
   - 来源：`tools/test.py`的`--score-thr`参数
   - 默认值：`0.1`
   - 作用：仅影响可视化显示，不影响实际预测结果
   - 代码位置：`tools/test.py`第89行

### 问题流程
```
用户设置 --score-thr 0.8
    ↓
只更新了 visualization_hook.score_thr = 0.8
    ↓
但 model.test_cfg.score_thr 仍然是 0
    ↓
模型NMS阶段几乎不过滤预测框
    ↓
大量低置信度预测框传递到可视化阶段
    ↓
即使可视化阈值是0.8，仍有很多框显示
```

## ✅ 解决方案

### 修复内容
在`tools/test.py`的`trigger_visualization_hook`函数中添加了以下代码：

```python
# IMPORTANT: Also update the model's test_cfg.score_thr for NMS filtering
# This ensures that the model itself filters predictions at the specified threshold
if hasattr(cfg, 'model') and hasattr(cfg.model, 'test_cfg'):
    cfg.model.test_cfg.score_thr = args.score_thr
    print(f"Updated model test_cfg.score_thr to {args.score_thr}")
```

### 修复后的流程
```
用户设置 --score-thr 0.8
    ↓
同时更新 visualization_hook.score_thr = 0.8
    ↓
同时更新 model.test_cfg.score_thr = 0.8
    ↓
模型NMS阶段过滤掉置信度 < 0.8 的预测框
    ↓
只有高置信度预测框传递到可视化阶段
    ↓
获得清晰的可视化效果
```

## 🎯 使用效果

### 修复前
```bash
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj --score-thr 0.8
# 结果：仍然有很多预测框，因为模型层面没有过滤
```

### 修复后
```bash
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj --score-thr 0.8
# 结果：显著减少预测框数量，只保留高置信度预测
```

## 📊 置信度阈值建议

| 阈值 | 预测框数量 | 可视化效果 | 适用场景 |
|------|------------|------------|----------|
| 0.1  | 很多       | 较杂乱     | 查看所有可能的预测 |
| 0.3  | 中等       | 平衡       | 默认推荐值 |
| 0.5  | 较少       | 清晰       | 高质量可视化 |
| 0.7  | 很少       | 非常清晰   | 只看最确信的预测 |
| 0.8+ | 极少       | 极简       | 只看极高置信度预测 |

## 🔧 技术细节

### NMS过滤代码位置
```python
# unidet3d/unidet3d.py 第734行
ids = scores[labels == class_id] > self.test_cfg.score_thr
```

### 配置文件默认值
```python
# configs/unidet3d_*.py
test_cfg=dict(
    low_sp_thr=0.18,
    up_sp_thr=0.81,
    topk_insts=1000,
    score_thr=0,  # 这里是问题所在
    iou_thr=[0.5, 0.55, 0.55, 0.55, 0.55, 0.55]
)
```

## 💡 其他优化建议

### 1. 非极大值抑制(NMS)
模型已经内置了NMS功能，通过以下参数控制：
- `iou_thr`: IoU阈值，控制重叠框的过滤
- `topk_insts`: 最大保留实例数量

### 2. 超点过滤
模型还使用超点进行额外过滤：
- `low_sp_thr`: 超点置信度下限
- `up_sp_thr`: 超点置信度上限

### 3. 进一步减少预测框
如果仍觉得预测框太多，可以考虑：
1. 提高置信度阈值到0.7-0.9
2. 降低`topk_insts`参数
3. 调整IoU阈值进行更严格的NMS

## 🎉 总结

这个修复确保了用户设置的`--score-thr`参数能够真正影响预测结果，而不仅仅是可视化显示。现在用户可以通过调整置信度阈值来精确控制预测框的数量，获得理想的可视化效果。
