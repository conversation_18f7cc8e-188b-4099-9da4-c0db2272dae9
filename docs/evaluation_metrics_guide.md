# UniDet3D 评估指标体系详细指南

## 目录
1. [概述](#概述)
2. [真值数据格式分析](#真值数据格式分析)
3. [真值数据生成过程](#真值数据生成过程)
4. [预测值数据格式](#预测值数据格式)
5. [评估指标详细定义](#评估指标详细定义)
6. [指标计算实现过程](#指标计算实现过程)
7. [代码实现分析](#代码实现分析)
8. [配置参数说明](#配置参数说明)

## 概述

UniDet3D项目采用基于3D包围盒的室内场景目标检测评估体系，支持多个数据集（ScanNet、S3DIS、MultiScan、3RScan、ScanNet++、ARKitScenes）的统一评估。评估系统主要包含以下核心组件：

- **真值数据处理**：从语义/实例分割标注生成3D包围盒
- **预测结果后处理**：NMS、置信度过滤等
- **IoU计算**：支持轴对齐和旋转包围盒的IoU计算
- **mAP评估**：基于不同IoU阈值的平均精度计算

## 真值数据格式分析

### 数据结构定义

真值数据在UniDet3D中以标准化格式存储：

```python
# 真值标注信息结构
ann_info = {
    'gt_bboxes_3d': DepthInstance3DBoxes,  # 3D包围盒
    'gt_labels_3d': np.ndarray,            # 类别标签
    'pts_instance_mask': np.ndarray,       # 实例分割掩码
    'pts_semantic_mask': np.ndarray,       # 语义分割掩码
    'sp_pts_mask': np.ndarray              # 超点掩码
}
```

### 3D包围盒格式

UniDet3D使用`DepthInstance3DBoxes`作为标准3D包围盒格式：

```python
# 包围盒参数 (6维或7维)
# 6维格式: [x, y, z, dx, dy, dz]
# 7维格式: [x, y, z, dx, dy, dz, yaw]
# 其中:
# - (x, y, z): 包围盒中心坐标
# - (dx, dy, dz): 包围盒尺寸 (长、宽、高)
# - yaw: 绕Z轴旋转角度 (可选)

# 坐标系统: Depth坐标系
# 原点设置: (0.5, 0.5, 0.5) - 包围盒中心作为参考点
```

### 坐标系统和单位

- **坐标系统**：Depth坐标系（相机坐标系）
- **单位**：米(m)
- **原点定义**：包围盒几何中心
- **轴向定义**：
  - X轴：水平向右
  - Y轴：垂直向下  
  - Z轴：深度向前

## 真值数据生成过程

### 从语义/实例分割到3D包围盒的转换

UniDet3D支持从语义和实例分割标注自动生成3D包围盒真值：

#### 1. 实例提取过程

```python
# 核心转换算法 (unidet3d/unidet3d.py:353-372)
def masks_to_boxes(self, masks, points):
    """从实例掩码生成3D包围盒"""
    boxes = []
    for mask in masks:
        # 提取属于该实例的点云
        object_points = points[mask]
        
        # 计算包围盒边界
        xyz_min = object_points.min(dim=0).values
        xyz_max = object_points.max(dim=0).values
        
        # 计算中心和尺寸
        center = (xyz_max + xyz_min) / 2
        size = xyz_max - xyz_min
        
        # 组合为6维包围盒
        box = torch.cat((center, size))
        boxes.append(box)
```

#### 2. 类别映射过程

```python
# 类别映射实现 (unidet3d/transforms_3d.py)
class PointDetClassMappingScanNet:
    def transform(self, input_dict):
        pts_instance_mask = torch.tensor(input_dict['pts_instance_mask'])
        pts_semantic_mask = torch.tensor(input_dict['pts_semantic_mask'])
        
        # 为每个实例分配语义类别
        for inst in unique_instances:
            # 获取实例对应的语义标签
            semantic_label = pts_semantic_mask[pts_instance_mask == inst][0]
            gt_labels[inst] = semantic_label - num_stuff_classes
```

### 数据预处理管道

不同数据集的预处理配置：

```python
# ScanNet数据集预处理管道
train_pipeline_scannet = [
    dict(type='LoadPointsFromFile', coord_type='DEPTH', use_color=True),
    dict(type='LoadAnnotations3D_', with_mask_3d=True, with_seg_3d=True),
    dict(type='GlobalAlignment', rotation_axis=2),
    dict(type='PointSegClassMapping'),
    dict(type='RandomFlip3D', flip_ratio_bev_horizontal=0.5),
    dict(type='GlobalRotScaleTrans', rot_range=[-3.14, 3.14]),
    dict(type='NormalizePointsColor_', color_mean=[127.5, 127.5, 127.5]),
    dict(type='PointDetClassMappingScanNet'),
    dict(type='Pack3DDetInputs_')
]
```

## 预测值数据格式

### 模型输出结构

```python
# 预测结果数据结构
pred_instances_3d = {
    'bboxes_3d': DepthInstance3DBoxes,  # 预测的3D包围盒
    'labels_3d': torch.Tensor,          # 预测类别标签
    'scores_3d': torch.Tensor,          # 置信度分数
    'dataset': str                      # 数据集标识
}
```

### 后处理步骤

#### 1. 非极大值抑制 (NMS)

```python
# NMS实现 (unidet3d/unidet3d.py:25-83)
def nms_3d_axis_aligned(bboxes, scores, iou_thr=0.5):
    """3D轴对齐包围盒NMS"""
    # 按置信度排序
    order = scores.argsort(descending=True)
    
    keep = []
    while len(order) > 0:
        i = order[0]
        keep.append(i)
        
        # 计算IoU
        bbox_i = bboxes_np[i]
        remaining_bboxes = bboxes_np[order[1:]]
        
        # 轴对齐IoU计算
        ious = compute_axis_aligned_iou(bbox_i, remaining_bboxes)
        
        # 保留IoU小于阈值的框
        inds = np.where(ious <= iou_thr)[0]
        order = order[inds + 1]
```

#### 2. 置信度过滤

```python
# 测试配置中的过滤参数
test_cfg = dict(
    low_sp_thr=0.18,      # 超点置信度下限
    up_sp_thr=0.81,       # 超点置信度上限  
    topk_insts=1000,      # 保留实例数量上限
    score_thr=0,          # 最低置信度阈值
    iou_thr=[0.5]         # NMS IoU阈值
)
```

## 评估指标详细定义

### 1. 平均精度 (Average Precision, AP)

AP是目标检测中最重要的评估指标，基于精确率-召回率曲线计算：

#### 数学定义

```
AP = ∫₀¹ P(R) dR
```

其中P(R)是召回率R对应的精确率。

#### 计算方法

UniDet3D支持两种AP计算模式：

1. **面积模式 (area)**：计算PR曲线下面积
2. **11点模式 (11points)**：在11个召回率点上采样

```python
# AP计算实现 (unidet3d/indoor_eval.py:8-53)
def average_precision(recalls, precisions, mode='area'):
    if mode == 'area':
        # 计算PR曲线下面积
        mrec = np.hstack((zeros, recalls, ones))
        mpre = np.hstack((zeros, precisions, zeros))
        
        # 单调化精确率曲线
        for i in range(mpre.shape[1] - 1, 0, -1):
            mpre[:, i - 1] = np.maximum(mpre[:, i - 1], mpre[:, i])
        
        # 计算面积
        for i in range(num_scales):
            ind = np.where(mrec[i, 1:] != mrec[i, :-1])[0]
            ap[i] = np.sum((mrec[i, ind + 1] - mrec[i, ind]) * mpre[i, ind + 1])
```

### 2. 平均平均精度 (mean Average Precision, mAP)

mAP是所有类别AP的平均值：

```
mAP = (1/N) × Σᵢ₌₁ᴺ APᵢ
```

其中N是类别数量，APᵢ是第i个类别的AP值。

### 3. IoU阈值设置

UniDet3D使用多个IoU阈值进行评估：

- **默认阈值**：[0.25, 0.5]
- **ScanNet标准**：0.25, 0.5
- **COCO风格**：0.5:0.05:0.95 (可配置)

## 指标计算实现过程

### 1. 预测-真值匹配策略

```python
# 匹配算法 (unidet3d/indoor_eval.py:119-147)
def match_predictions_to_ground_truth(pred_boxes, gt_boxes, iou_threshold):
    """预测框与真值框匹配"""
    for detection in sorted_detections:
        # 计算与所有真值框的IoU
        ious = compute_iou(detection.bbox, gt_boxes)
        
        # 找到最大IoU的真值框
        max_iou = max(ious)
        max_idx = argmax(ious)
        
        # 判断是否为真正例
        if max_iou > iou_threshold:
            if not gt_boxes[max_idx].matched:
                # 真正例 (TP)
                tp[detection_idx] = 1
                gt_boxes[max_idx].matched = True
            else:
                # 假正例 (FP) - 重复检测
                fp[detection_idx] = 1
        else:
            # 假正例 (FP) - IoU不足
            fp[detection_idx] = 1
```

### 2. IoU计算实现

#### 轴对齐包围盒IoU

```python
# 3D轴对齐IoU计算 (unidet3d/unidet3d.py:58-77)
def compute_axis_aligned_iou_3d(bbox1, bbox2):
    """计算两个轴对齐3D包围盒的IoU"""
    # 提取中心和尺寸
    center1, size1 = bbox1[:3], bbox1[3:6]
    center2, size2 = bbox2[:3], bbox2[3:6]
    
    # 计算边界
    min1, max1 = center1 - size1/2, center1 + size1/2
    min2, max2 = center2 - size2/2, center2 + size2/2
    
    # 交集计算
    inter_min = np.maximum(min1, min2)
    inter_max = np.minimum(max1, max2)
    inter_size = np.maximum(0, inter_max - inter_min)
    inter_volume = np.prod(inter_size)
    
    # 并集计算
    volume1 = np.prod(size1)
    volume2 = np.prod(size2)
    union_volume = volume1 + volume2 - inter_volume
    
    # IoU
    iou = inter_volume / (union_volume + 1e-8)
    return iou
```

#### 旋转包围盒IoU

```python
# 旋转3D包围盒IoU (unidet3d/rotated_iou_loss.py:14-35)
def diff_diou_rotated_3d(box3d1, box3d2):
    """计算旋转3D包围盒的DIoU"""
    # 2D投影IoU计算
    box1_2d = box3d1[..., [0, 1, 3, 4, 6]]  # x,y,w,h,yaw
    box2_2d = box3d2[..., [0, 1, 3, 4, 6]]
    
    corners1 = box2corners(box1_2d)
    corners2 = box2corners(box2_2d)
    intersection_2d, _ = oriented_box_intersection_2d(corners1, corners2)
    
    # Z轴重叠计算
    z_overlap = compute_z_overlap(box3d1, box3d2)
    
    # 3D交集
    intersection_3d = intersection_2d * z_overlap
    
    # 3D并集和IoU
    volume1 = box3d1[..., 3] * box3d1[..., 4] * box3d1[..., 5]
    volume2 = box3d2[..., 3] * box3d2[..., 4] * box3d2[..., 5]
    union_3d = volume1 + volume2 - intersection_3d
    
    iou_3d = intersection_3d / (union_3d + 1e-8)
    return iou_3d
```

### 3. 精确率和召回率计算

```python
# PR计算 (unidet3d/indoor_eval.py:148-160)
def compute_precision_recall(tp, fp, num_gt):
    """计算精确率和召回率"""
    # 累积TP和FP
    tp_cumsum = np.cumsum(tp)
    fp_cumsum = np.cumsum(fp)
    
    # 召回率 = TP / (TP + FN) = TP / num_gt
    recalls = tp_cumsum / float(num_gt)
    
    # 精确率 = TP / (TP + FP)
    precisions = tp_cumsum / np.maximum(tp_cumsum + fp_cumsum, 1e-8)
    
    return recalls, precisions
```

## 代码实现分析

### 核心文件结构

```
unidet3d/
├── indoor_eval.py          # 评估算法核心实现
├── indoor_metric.py        # 评估指标封装类
├── axis_aligned_iou_loss.py # 轴对齐IoU损失
├── rotated_iou_loss.py     # 旋转IoU损失  
├── show_results.py         # 结果可视化
└── unidet3d.py            # 主模型和NMS实现
```

### 关键类和函数

#### 1. IndoorMetric_ 类

```python
# 评估指标管理类 (unidet3d/indoor_metric.py:14-42)
@METRICS.register_module()
class IndoorMetric_(BaseMetric):
    """室内场景评估指标类"""
    
    def __init__(self, datasets, datasets_classes, 
                 iou_thr=[0.25, 0.5], vis_dir=None):
        self.iou_thr = iou_thr
        self.datasets = datasets
        self.datasets_classes = datasets_classes
        self.vis_dir = vis_dir
    
    def process(self, data_batch, data_samples):
        """处理单批次数据"""
        # 提取预测结果和真值标注
        # 转换为CPU张量便于计算
        
    def compute_metrics(self, results):
        """计算最终评估指标"""
        # 按数据集分组
        # 调用indoor_eval进行评估
        # 返回mAP等指标
```

#### 2. indoor_eval 函数

```python
# 主评估函数 (unidet3d/indoor_eval.py:203-229)
def indoor_eval(gt_annos, dt_annos, metric, label2cat, 
                logger=None, box_mode_3d=None):
    """室内场景评估主函数"""
    
    # 1. 数据格式转换
    pred = {}  # {class_id: {img_id: [(bbox, score)]}}
    gt = {}    # {class_id: {img_id: [bbox]}}
    
    # 2. 解析预测结果
    for img_id, det_anno in enumerate(dt_annos):
        for i, (label, bbox, score) in enumerate(zip(
            det_anno['labels_3d'], 
            det_anno['bboxes_3d'], 
            det_anno['scores_3d'])):
            pred[label][img_id].append((bbox, score))
    
    # 3. 解析真值标注  
    for img_id, gt_anno in enumerate(gt_annos):
        for i, (label, bbox) in enumerate(zip(
            gt_anno['gt_labels_3d'],
            gt_anno['gt_bboxes_3d'])):
            gt[label][img_id].append(bbox)
    
    # 4. 计算评估指标
    rec, prec, ap = eval_map_recall(pred, gt, metric)
    
    return format_results(rec, prec, ap, label2cat)
```

## 配置参数说明

### 评估相关配置

```python
# 模型测试配置
test_cfg = dict(
    low_sp_thr=0.18,      # 超点置信度下限
    up_sp_thr=0.81,       # 超点置信度上限
    topk_insts=1000,      # 最大实例数量
    score_thr=0,          # 最低置信度阈值  
    iou_thr=[0.5]         # NMS IoU阈值
)

# 评估指标配置
val_evaluator = dict(
    type='IndoorMetric_',
    datasets=['scannet', 's3dis', 'multiscan'],
    datasets_classes=[scannet_classes, s3dis_classes, multiscan_classes],
    iou_thr=[0.25, 0.5],  # 评估IoU阈值
    vis_dir='./vis_results'  # 可视化输出目录
)
```

### 数据集特定配置

```python
# ScanNet数据集配置
scannet_classes = ('cabinet', 'bed', 'chair', 'sofa', 'table', 
                   'door', 'window', 'bookshelf', 'picture', 
                   'counter', 'desk', 'curtain', 'refrigerator', 
                   'shower curtain', 'toilet', 'sink', 'bathtub', 
                   'otherfurniture')

# S3DIS数据集配置  
s3dis_classes = ('table', 'chair', 'sofa', 'bookcase', 'board')

# 类别映射配置
class_mapping = {
    'scannet': {7: 0, 8: 1, 9: 2, 10: 3, 11: 4},  # door, window等
    's3dis': {7: 0, 8: 1, 9: 2, 10: 3, 11: 4}
}
```

---

**注意**：本文档基于UniDet3D项目的当前实现，具体参数和配置可能随版本更新而变化。建议结合最新的配置文件和代码进行使用。

## 数据格式示例

### 真值数据示例

```python
# ScanNet场景真值数据示例
gt_anno = {
    'gt_bboxes_3d': DepthInstance3DBoxes([
        [1.2, 0.8, 1.5, 0.8, 0.1, 2.0],  # 门: 中心(1.2,0.8,1.5), 尺寸(0.8,0.1,2.0)
        [2.5, 1.2, 1.0, 1.5, 0.1, 1.2],  # 窗: 中心(2.5,1.2,1.0), 尺寸(1.5,0.1,1.2)
    ]),
    'gt_labels_3d': np.array([5, 6]),  # 类别: 5=door, 6=window
    'pts_instance_mask': np.array([...]),  # 点级实例掩码
    'pts_semantic_mask': np.array([...]),   # 点级语义掩码
}
```

### 预测结果示例

```python
# 模型预测结果示例
pred_result = {
    'bboxes_3d': DepthInstance3DBoxes([
        [1.15, 0.82, 1.48, 0.85, 0.12, 1.98],  # 预测门
        [2.48, 1.18, 1.02, 1.52, 0.09, 1.18],  # 预测窗
        [0.5, 2.1, 0.8, 0.6, 0.6, 1.8],        # 误检测
    ]),
    'labels_3d': torch.tensor([5, 6, 5]),      # 预测类别
    'scores_3d': torch.tensor([0.92, 0.87, 0.45]),  # 置信度
}
```

## 评估流程图

```mermaid
graph TD
    A[输入数据] --> B[数据预处理]
    B --> C[模型推理]
    C --> D[后处理NMS]
    D --> E[格式转换]
    E --> F[IoU计算]
    F --> G[TP/FP判断]
    G --> H[PR曲线计算]
    H --> I[AP计算]
    I --> J[mAP汇总]

    B --> B1[点云加载]
    B --> B2[标注加载]
    B --> B3[数据增强]

    D --> D1[置信度过滤]
    D --> D2[NMS去重]

    F --> F1[轴对齐IoU]
    F --> F2[旋转IoU]

    I --> I1[面积模式]
    I --> I2[11点模式]
```

## 计算示例

### IoU计算示例

```python
# 示例：计算两个3D包围盒的IoU
bbox_pred = [1.0, 1.0, 1.0, 2.0, 2.0, 2.0]  # 预测框
bbox_gt   = [1.2, 1.1, 0.9, 1.8, 1.9, 2.1]  # 真值框

# 步骤1: 计算边界
pred_min = [1.0-1.0, 1.0-1.0, 1.0-1.0] = [0.0, 0.0, 0.0]
pred_max = [1.0+1.0, 1.0+1.0, 1.0+1.0] = [2.0, 2.0, 2.0]
gt_min   = [1.2-0.9, 1.1-0.95, 0.9-1.05] = [0.3, 0.15, -0.15]
gt_max   = [1.2+0.9, 1.1+0.95, 0.9+1.05] = [2.1, 2.05, 1.95]

# 步骤2: 计算交集
inter_min = max([0.0, 0.0, 0.0], [0.3, 0.15, -0.15]) = [0.3, 0.15, 0.0]
inter_max = min([2.0, 2.0, 2.0], [2.1, 2.05, 1.95]) = [2.0, 2.0, 1.95]
inter_size = [2.0-0.3, 2.0-0.15, 1.95-0.0] = [1.7, 1.85, 1.95]
inter_volume = 1.7 × 1.85 × 1.95 = 6.13

# 步骤3: 计算并集
pred_volume = 2.0 × 2.0 × 2.0 = 8.0
gt_volume = 1.8 × 1.9 × 2.1 = 7.182
union_volume = 8.0 + 7.182 - 6.13 = 9.052

# 步骤4: 计算IoU
iou = 6.13 / 9.052 = 0.677
```

### mAP计算示例

```python
# 示例：单类别AP计算
# 假设有3个真值框，5个预测框
predictions = [
    {'bbox': bbox1, 'score': 0.9, 'matched': True},   # TP
    {'bbox': bbox2, 'score': 0.8, 'matched': False},  # FP
    {'bbox': bbox3, 'score': 0.7, 'matched': True},   # TP
    {'bbox': bbox4, 'score': 0.6, 'matched': True},   # TP
    {'bbox': bbox5, 'score': 0.5, 'matched': False},  # FP
]

# 累积TP和FP
tp_cumsum = [1, 1, 2, 3, 3]  # 累积真正例
fp_cumsum = [0, 1, 1, 1, 2]  # 累积假正例

# 计算精确率和召回率
num_gt = 3
precisions = [1.0, 0.5, 0.67, 0.75, 0.6]  # TP/(TP+FP)
recalls = [0.33, 0.33, 0.67, 1.0, 1.0]    # TP/num_gt

# 计算AP (面积模式)
# 单调化精确率: [1.0, 0.75, 0.75, 0.75, 0.6]
# AP = 积分面积 ≈ 0.78
```

## 可视化输出

UniDet3D支持将评估结果输出为.obj格式文件，便于在MeshLab中查看：

### 输出文件结构

```
vis_results/
├── scannet/
│   ├── scene0001_00/
│   │   ├── scene0001_00_points.obj    # 点云
│   │   ├── scene0001_00_gt.obj        # 真值框
│   │   └── scene0001_00_pred.obj      # 预测框
│   └── scene0002_00/
└── s3dis/
```

### 可视化配置

```python
# 可视化相关配置
vis_config = {
    'score_thr': 0.3,        # 显示置信度阈值
    'max_boxes': 100,        # 最大显示框数
    'color_scheme': 'class', # 颜色方案: 'class' 或 'confidence'
    'show_gt': True,         # 显示真值框
    'show_pred': True,       # 显示预测框
}
```

## 性能优化建议

### 1. 评估加速

```python
# 使用GPU加速IoU计算
device = 'cuda' if torch.cuda.is_available() else 'cpu'
pred_boxes = pred_boxes.to(device)
gt_boxes = gt_boxes.to(device)

# 批量IoU计算
iou_matrix = batch_iou_3d(pred_boxes, gt_boxes)
```

### 2. 内存优化

```python
# 分批处理大场景
batch_size = 1000
for i in range(0, len(predictions), batch_size):
    batch_pred = predictions[i:i+batch_size]
    batch_results = evaluate_batch(batch_pred, gt_annotations)
```

### 3. 多进程评估

```python
# 并行评估多个数据集
from multiprocessing import Pool

def evaluate_dataset(dataset_name):
    return indoor_eval(gt_data[dataset_name], pred_data[dataset_name])

with Pool(processes=4) as pool:
    results = pool.map(evaluate_dataset, dataset_names)
```

## 常见问题和解决方案

### 1. 坐标系不匹配

**问题**：预测框和真值框坐标系不一致导致IoU计算错误。

**解决方案**：
```python
# 确保坐标系一致
pred_boxes = pred_boxes.convert_to(box_mode_3d)
gt_boxes = gt_boxes.convert_to(box_mode_3d)
```

### 2. 类别映射错误

**问题**：不同数据集的类别标签不一致。

**解决方案**：
```python
# 使用统一的类别映射
label_mapping = {
    'scannet': {5: 0, 6: 1},  # door->0, window->1
    's3dis': {7: 0, 8: 1}     # door->0, window->1
}
```

### 3. 内存溢出

**问题**：大场景评估时内存不足。

**解决方案**：
```python
# 分块处理
def chunked_evaluation(predictions, ground_truth, chunk_size=1000):
    results = []
    for i in range(0, len(predictions), chunk_size):
        chunk_pred = predictions[i:i+chunk_size]
        chunk_result = evaluate_chunk(chunk_pred, ground_truth)
        results.append(chunk_result)
    return merge_results(results)
```

## 数据格式示例

### 真值数据示例

```python
# ScanNet场景真值数据示例
gt_anno = {
    'gt_bboxes_3d': DepthInstance3DBoxes([
        [1.2, 0.8, 1.5, 0.8, 0.1, 2.0],  # 门: 中心(1.2,0.8,1.5), 尺寸(0.8,0.1,2.0)
        [2.5, 1.2, 1.0, 1.5, 0.1, 1.2],  # 窗: 中心(2.5,1.2,1.0), 尺寸(1.5,0.1,1.2)
    ]),
    'gt_labels_3d': np.array([5, 6]),  # 类别: 5=door, 6=window
    'pts_instance_mask': np.array([...]),  # 点级实例掩码
    'pts_semantic_mask': np.array([...]),   # 点级语义掩码
}
```

### 预测结果示例

```python
# 模型预测结果示例
pred_result = {
    'bboxes_3d': DepthInstance3DBoxes([
        [1.15, 0.82, 1.48, 0.85, 0.12, 1.98],  # 预测门
        [2.48, 1.18, 1.02, 1.52, 0.09, 1.18],  # 预测窗
        [0.5, 2.1, 0.8, 0.6, 0.6, 1.8],        # 误检测
    ]),
    'labels_3d': torch.tensor([5, 6, 5]),      # 预测类别
    'scores_3d': torch.tensor([0.92, 0.87, 0.45]),  # 置信度
}
```

## 评估流程图

### 整体评估流程

```mermaid
graph TD
    A[输入数据] --> B[数据预处理]
    B --> C[模型推理]
    C --> D[后处理NMS]
    D --> E[格式转换]
    E --> F[IoU计算]
    F --> G[TP/FP判断]
    G --> H[PR曲线计算]
    H --> I[AP计算]
    I --> J[mAP汇总]

    B --> B1[点云加载]
    B --> B2[标注加载]
    B --> B3[数据增强]

    D --> D1[置信度过滤]
    D --> D2[NMS去重]

    F --> F1[轴对齐IoU]
    F --> F2[旋转IoU]

    I --> I1[面积模式]
    I --> I2[11点模式]
```

### 数据转换流程

```mermaid
graph LR
    A[语义分割] --> B[实例分割]
    B --> C[实例提取]
    C --> D[包围盒生成]
    D --> E[类别映射]
    E --> F[格式标准化]

    C --> C1[点云聚类]
    C --> C2[边界计算]

    D --> D1[中心计算]
    D --> D2[尺寸计算]
    D --> D3[方向估计]
```

## 计算示例

### IoU计算详细示例

```python
# 示例：计算两个3D包围盒的IoU
bbox_pred = [1.0, 1.0, 1.0, 2.0, 2.0, 2.0]  # 预测框: [x,y,z,dx,dy,dz]
bbox_gt   = [1.2, 1.1, 0.9, 1.8, 1.9, 2.1]  # 真值框: [x,y,z,dx,dy,dz]

# 步骤1: 计算包围盒边界
pred_min = [1.0-1.0, 1.0-1.0, 1.0-1.0] = [0.0, 0.0, 0.0]
pred_max = [1.0+1.0, 1.0+1.0, 1.0+1.0] = [2.0, 2.0, 2.0]
gt_min   = [1.2-0.9, 1.1-0.95, 0.9-1.05] = [0.3, 0.15, -0.15]
gt_max   = [1.2+0.9, 1.1+0.95, 0.9+1.05] = [2.1, 2.05, 1.95]

# 步骤2: 计算交集区域
inter_min = max([0.0, 0.0, 0.0], [0.3, 0.15, -0.15]) = [0.3, 0.15, 0.0]
inter_max = min([2.0, 2.0, 2.0], [2.1, 2.05, 1.95]) = [2.0, 2.0, 1.95]
inter_size = [2.0-0.3, 2.0-0.15, 1.95-0.0] = [1.7, 1.85, 1.95]
inter_volume = 1.7 × 1.85 × 1.95 = 6.13

# 步骤3: 计算并集体积
pred_volume = 2.0 × 2.0 × 2.0 = 8.0
gt_volume = 1.8 × 1.9 × 2.1 = 7.182
union_volume = 8.0 + 7.182 - 6.13 = 9.052

# 步骤4: 计算IoU
iou = 6.13 / 9.052 = 0.677
```

### mAP计算完整示例

```python
# 示例：单类别AP计算过程
# 假设有3个真值框，5个预测框，IoU阈值=0.5

# 预测结果（按置信度降序排列）
predictions = [
    {'bbox': bbox1, 'score': 0.9, 'iou_with_gt': [0.8, 0.1, 0.2]},  # 与GT1匹配
    {'bbox': bbox2, 'score': 0.8, 'iou_with_gt': [0.2, 0.3, 0.1]},  # 无匹配
    {'bbox': bbox3, 'score': 0.7, 'iou_with_gt': [0.1, 0.7, 0.2]},  # 与GT2匹配
    {'bbox': bbox4, 'score': 0.6, 'iou_with_gt': [0.3, 0.2, 0.6]},  # 与GT3匹配
    {'bbox': bbox5, 'score': 0.5, 'iou_with_gt': [0.4, 0.3, 0.2]},  # 无匹配
]

# 匹配过程
tp = [1, 0, 1, 1, 0]  # 真正例标记
fp = [0, 1, 0, 0, 1]  # 假正例标记

# 累积计算
tp_cumsum = [1, 1, 2, 3, 3]  # 累积真正例
fp_cumsum = [0, 1, 1, 1, 2]  # 累积假正例

# 精确率和召回率
num_gt = 3
precisions = [1.0, 0.5, 0.67, 0.75, 0.6]  # TP/(TP+FP)
recalls = [0.33, 0.33, 0.67, 1.0, 1.0]    # TP/num_gt

# AP计算（面积模式）
# 1. 添加端点：recalls=[0, 0.33, 0.33, 0.67, 1.0, 1.0, 1]
#              precisions=[0, 1.0, 0.5, 0.67, 0.75, 0.6, 0]
# 2. 单调化精确率：[0, 1.0, 1.0, 0.75, 0.75, 0.6, 0]
# 3. 计算面积：AP = (0.33-0)×1.0 + (0.67-0.33)×0.75 + (1.0-0.67)×0.6 = 0.783
```

## 可视化输出

UniDet3D支持将评估结果输出为.obj格式文件，便于在MeshLab中查看：

### 输出文件结构

```
vis_results/
├── scannet/
│   ├── scene0001_00/
│   │   ├── scene0001_00_points.obj    # 点云数据
│   │   ├── scene0001_00_gt.obj        # 真值包围盒
│   │   └── scene0001_00_pred.obj      # 预测包围盒
│   └── scene0002_00/
│       ├── scene0002_00_points.obj
│       ├── scene0002_00_gt.obj
│       └── scene0002_00_pred.obj
└── s3dis/
    └── Area_1_office_1/
        ├── Area_1_office_1_points.obj
        ├── Area_1_office_1_gt.obj
        └── Area_1_office_1_pred.obj
```

### 可视化配置

```python
# 可视化相关配置
vis_config = {
    'score_thr': 0.3,        # 显示置信度阈值（推荐0.3以获得更好效果）
    'max_boxes': 100,        # 最大显示框数量
    'color_scheme': 'class', # 颜色方案: 'class'按类别着色 或 'confidence'按置信度着色
    'show_gt': True,         # 显示真值框（蓝色）
    'show_pred': True,       # 显示预测框（按类别着色）
    'point_size': 2,         # 点云显示大小
}

# 在配置文件中启用可视化
val_evaluator = dict(
    type='IndoorMetric_',
    datasets=['scannet'],
    datasets_classes=[scannet_classes],
    iou_thr=[0.25, 0.5],
    vis_dir='./vis_results'  # 指定可视化输出目录
)
```

### MeshLab查看步骤

1. **打开MeshLab**
2. **导入点云**：File → Import Mesh → 选择 `*_points.obj`
3. **导入真值框**：File → Import Mesh → 选择 `*_gt.obj`
4. **导入预测框**：File → Import Mesh → 选择 `*_pred.obj`
5. **调整显示**：
   - 点云：Render → Show Points，调整点大小
   - 包围盒：Render → Show Wireframe，显示线框模式

## 性能优化建议

### 1. 评估加速技巧

```python
# GPU加速IoU计算
def accelerated_evaluation(pred_boxes, gt_boxes):
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    pred_boxes = pred_boxes.to(device)
    gt_boxes = gt_boxes.to(device)

    # 批量IoU计算，避免循环
    iou_matrix = batch_iou_3d(pred_boxes, gt_boxes)
    return iou_matrix

# 预计算IoU矩阵
def precompute_iou_matrix(predictions, ground_truths):
    """预计算所有预测框与真值框的IoU矩阵"""
    n_pred = len(predictions)
    n_gt = len(ground_truths)
    iou_matrix = torch.zeros(n_pred, n_gt)

    for i, pred_box in enumerate(predictions):
        for j, gt_box in enumerate(ground_truths):
            iou_matrix[i, j] = compute_iou_3d(pred_box, gt_box)

    return iou_matrix
```

### 2. 内存优化策略

```python
# 分批处理大场景
def memory_efficient_evaluation(predictions, ground_truths, batch_size=1000):
    """内存友好的评估方法"""
    total_results = []

    for i in range(0, len(predictions), batch_size):
        batch_pred = predictions[i:i+batch_size]

        # 处理当前批次
        batch_results = evaluate_batch(batch_pred, ground_truths)
        total_results.extend(batch_results)

        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    return merge_results(total_results)

# 渐进式加载
def progressive_loading(data_paths, max_memory_gb=8):
    """根据内存限制渐进式加载数据"""
    current_memory = 0
    loaded_data = []

    for path in data_paths:
        data_size = estimate_data_size(path)
        if current_memory + data_size > max_memory_gb * 1024**3:
            # 处理当前批次
            yield loaded_data
            loaded_data = []
            current_memory = 0

        loaded_data.append(load_data(path))
        current_memory += data_size

    if loaded_data:
        yield loaded_data
```

### 3. 多进程并行评估

```python
# 并行评估多个数据集
from multiprocessing import Pool, Manager
import functools

def parallel_dataset_evaluation(datasets_config):
    """并行评估多个数据集"""

    def evaluate_single_dataset(dataset_name, shared_results):
        try:
            # 加载数据集特定配置
            config = datasets_config[dataset_name]

            # 执行评估
            results = indoor_eval(
                config['gt_data'],
                config['pred_data'],
                config['iou_thresholds'],
                config['label_mapping']
            )

            # 存储结果
            shared_results[dataset_name] = results
            print(f"完成 {dataset_name} 数据集评估")

        except Exception as e:
            print(f"评估 {dataset_name} 时出错: {e}")
            shared_results[dataset_name] = None

    # 创建共享结果字典
    manager = Manager()
    shared_results = manager.dict()

    # 创建进程池
    with Pool(processes=min(4, len(datasets_config))) as pool:
        # 为每个数据集创建评估任务
        tasks = []
        for dataset_name in datasets_config.keys():
            task = functools.partial(
                evaluate_single_dataset,
                dataset_name,
                shared_results
            )
            tasks.append(pool.apply_async(task))

        # 等待所有任务完成
        for task in tasks:
            task.get()

    return dict(shared_results)

# 使用示例
datasets_config = {
    'scannet': {
        'gt_data': scannet_gt,
        'pred_data': scannet_pred,
        'iou_thresholds': [0.25, 0.5],
        'label_mapping': scannet_label_mapping
    },
    's3dis': {
        'gt_data': s3dis_gt,
        'pred_data': s3dis_pred,
        'iou_thresholds': [0.25, 0.5],
        'label_mapping': s3dis_label_mapping
    }
}

results = parallel_dataset_evaluation(datasets_config)
```

## 常见问题和解决方案

### 1. 坐标系不匹配问题

**问题描述**：预测框和真值框坐标系不一致导致IoU计算错误。

**症状**：
- IoU值异常低（接近0）
- 可视化中预测框位置明显错误
- mAP指标异常低

**解决方案**：
```python
# 方法1: 统一坐标系转换
def ensure_coordinate_consistency(pred_boxes, gt_boxes, target_mode='Depth'):
    """确保预测框和真值框使用相同坐标系"""
    if hasattr(pred_boxes, 'convert_to'):
        pred_boxes = pred_boxes.convert_to(target_mode)
    if hasattr(gt_boxes, 'convert_to'):
        gt_boxes = gt_boxes.convert_to(target_mode)
    return pred_boxes, gt_boxes

# 方法2: 检查坐标系一致性
def validate_coordinate_system(boxes1, boxes2):
    """验证两组包围盒的坐标系是否一致"""
    # 检查数值范围
    range1 = boxes1.tensor.max(dim=0)[0] - boxes1.tensor.min(dim=0)[0]
    range2 = boxes2.tensor.max(dim=0)[0] - boxes2.tensor.min(dim=0)[0]

    # 检查是否在合理范围内
    if torch.any(range1 > 1000) or torch.any(range2 > 1000):
        print("警告：检测到异常大的坐标值，可能存在坐标系问题")

    return True
```

### 2. 类别映射错误

**问题描述**：不同数据集的类别标签编码不一致。

**解决方案**：
```python
# 创建统一的类别映射系统
class UnifiedLabelMapper:
    def __init__(self):
        self.dataset_mappings = {
            'scannet': {
                'door': 5, 'window': 6, 'chair': 2, 'table': 4,
                'bed': 1, 'sofa': 3, 'cabinet': 0
            },
            's3dis': {
                'door': 7, 'window': 8, 'chair': 9, 'table': 10, 'board': 11
            },
            'multiscan': {
                'door': 12, 'window': 13, 'chair': 14, 'table': 15
            }
        }

        # 统一标签（目标检测类别）
        self.unified_labels = {
            'door': 0, 'window': 1, 'chair': 2, 'table': 3,
            'bed': 4, 'sofa': 5, 'cabinet': 6, 'board': 7
        }

    def map_to_unified(self, labels, dataset_name):
        """将数据集特定标签映射到统一标签"""
        dataset_mapping = self.dataset_mappings[dataset_name]
        reverse_mapping = {v: k for k, v in dataset_mapping.items()}

        unified_labels = []
        for label in labels:
            class_name = reverse_mapping.get(label, 'unknown')
            unified_label = self.unified_labels.get(class_name, -1)
            unified_labels.append(unified_label)

        return np.array(unified_labels)

# 使用示例
mapper = UnifiedLabelMapper()
unified_gt_labels = mapper.map_to_unified(gt_labels, 'scannet')
unified_pred_labels = mapper.map_to_unified(pred_labels, 'scannet')
```

### 3. 内存溢出问题

**问题描述**：评估大场景或多数据集时出现内存不足。

**解决方案**：
```python
# 内存监控和管理
import psutil
import gc

class MemoryManager:
    def __init__(self, max_memory_percent=80):
        self.max_memory_percent = max_memory_percent

    def check_memory_usage(self):
        """检查当前内存使用率"""
        memory_percent = psutil.virtual_memory().percent
        return memory_percent

    def cleanup_if_needed(self):
        """必要时清理内存"""
        if self.check_memory_usage() > self.max_memory_percent:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            print(f"内存清理完成，当前使用率: {self.check_memory_usage():.1f}%")

    def chunked_evaluation(self, data, chunk_size=None):
        """分块评估以控制内存使用"""
        if chunk_size is None:
            # 根据内存情况动态调整块大小
            available_memory_gb = psutil.virtual_memory().available / (1024**3)
            chunk_size = max(100, int(available_memory_gb * 100))

        results = []
        for i in range(0, len(data), chunk_size):
            chunk = data[i:i+chunk_size]
            chunk_result = self.process_chunk(chunk)
            results.extend(chunk_result)

            # 每处理完一个块就检查内存
            self.cleanup_if_needed()

        return results

# 使用示例
memory_manager = MemoryManager(max_memory_percent=75)
results = memory_manager.chunked_evaluation(large_dataset)
```

### 4. 评估速度慢问题

**解决方案**：
```python
# 性能分析和优化
import time
import cProfile

def profile_evaluation(func, *args, **kwargs):
    """性能分析装饰器"""
    start_time = time.time()

    # 使用cProfile进行详细分析
    profiler = cProfile.Profile()
    profiler.enable()

    result = func(*args, **kwargs)

    profiler.disable()
    end_time = time.time()

    print(f"评估耗时: {end_time - start_time:.2f}秒")

    # 输出性能分析报告
    profiler.print_stats(sort='cumulative')

    return result

# 缓存IoU计算结果
from functools import lru_cache

@lru_cache(maxsize=10000)
def cached_iou_computation(box1_tuple, box2_tuple):
    """缓存IoU计算结果"""
    box1 = torch.tensor(box1_tuple)
    box2 = torch.tensor(box2_tuple)
    return compute_iou_3d(box1, box2).item()

# 向量化操作优化
def vectorized_iou_computation(pred_boxes, gt_boxes):
    """向量化IoU计算"""
    # 使用广播机制一次性计算所有IoU
    pred_expanded = pred_boxes.unsqueeze(1)  # [N, 1, 6]
    gt_expanded = gt_boxes.unsqueeze(0)      # [1, M, 6]

    # 批量计算所有组合的IoU
    iou_matrix = batch_compute_iou_3d(pred_expanded, gt_expanded)
    return iou_matrix  # [N, M]
```

## 完整配置示例

### 单数据集评估配置

```python
# configs/eval_scannet_only.py
# ScanNet数据集专用评估配置

# 数据集配置
dataset_type = 'ScanNetDetDataset'
data_root = 'data/scannet/'
class_names = ('cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window',
               'bookshelf', 'picture', 'counter', 'desk', 'curtain',
               'refrigerator', 'shower curtain', 'toilet', 'sink', 'bathtub',
               'otherfurniture')

# 评估管道配置
val_pipeline = [
    dict(type='LoadPointsFromFile', coord_type='DEPTH', use_color=True,
         load_dim=6, use_dim=[0, 1, 2, 3, 4, 5]),
    dict(type='LoadAnnotations3D_', with_bbox_3d=True, with_label_3d=True,
         with_sp_mask_3d=True),
    dict(type='Pack3DDetInputs_', keys=['points', 'gt_bboxes_3d', 'gt_labels_3d'])
]

# 数据加载器配置
val_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        data_root=data_root,
        ann_file='scannet_infos_val.pkl',
        pipeline=val_pipeline,
        test_mode=True,
        box_type_3d='Depth'
    )
)

# 评估器配置
val_evaluator = dict(
    type='IndoorMetric_',
    datasets=['scannet'],
    datasets_classes=[class_names],
    iou_thr=[0.25, 0.5],  # ScanNet标准IoU阈值
    vis_dir='./work_dirs/vis_scannet',  # 可视化输出目录
    collect_device='cpu'
)

# 测试配置
test_cfg = dict(
    low_sp_thr=0.18,
    up_sp_thr=0.81,
    topk_insts=1000,
    score_thr=0.0,
    iou_thr=[0.5]
)
```

### 多数据集联合评估配置

```python
# configs/eval_multi_datasets.py
# 多数据集联合评估配置

# 数据集类别定义
scannet_classes = ('cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window',
                   'bookshelf', 'picture', 'counter', 'desk', 'curtain',
                   'refrigerator', 'shower curtain', 'toilet', 'sink', 'bathtub',
                   'otherfurniture')

s3dis_classes = ('table', 'chair', 'sofa', 'bookcase', 'board')

multiscan_classes = ('cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window')

# 多数据集评估器
val_evaluator = dict(
    type='IndoorMetric_',
    datasets=['scannet', 's3dis', 'multiscan'],
    datasets_classes=[scannet_classes, s3dis_classes, multiscan_classes],
    iou_thr=[0.25, 0.5],
    vis_dir='./work_dirs/vis_multi_datasets'
)

# 数据集特定配置
dataset_configs = {
    'scannet': {
        'data_root': 'data/scannet/',
        'ann_file': 'scannet_infos_val.pkl',
        'pipeline': scannet_val_pipeline
    },
    's3dis': {
        'data_root': 'data/s3dis/',
        'ann_file': 's3dis_infos_val.pkl',
        'pipeline': s3dis_val_pipeline
    },
    'multiscan': {
        'data_root': 'data/multiscan/',
        'ann_file': 'multiscan_infos_val.pkl',
        'pipeline': multiscan_val_pipeline
    }
}
```

### 自定义评估脚本

```python
# tools/custom_evaluation.py
# 自定义评估脚本示例

import argparse
import torch
from mmengine.config import Config
from mmengine.runner import Runner
from unidet3d import IndoorMetric_

def parse_args():
    parser = argparse.ArgumentParser(description='UniDet3D评估脚本')
    parser.add_argument('config', help='配置文件路径')
    parser.add_argument('checkpoint', help='模型权重文件路径')
    parser.add_argument('--work-dir', help='工作目录')
    parser.add_argument('--vis-dir', help='可视化输出目录')
    parser.add_argument('--score-thr', type=float, default=0.3,
                       help='置信度阈值')
    parser.add_argument('--iou-thr', type=float, nargs='+',
                       default=[0.25, 0.5], help='IoU阈值列表')
    parser.add_argument('--datasets', nargs='+',
                       default=['scannet'], help='评估数据集列表')
    return parser.parse_args()

def main():
    args = parse_args()

    # 加载配置
    cfg = Config.fromfile(args.config)

    # 更新配置
    if args.work_dir:
        cfg.work_dir = args.work_dir

    # 更新评估器配置
    if args.vis_dir:
        cfg.val_evaluator.vis_dir = args.vis_dir

    cfg.val_evaluator.iou_thr = args.iou_thr
    cfg.val_evaluator.datasets = args.datasets

    # 更新测试配置
    cfg.model.test_cfg.score_thr = args.score_thr

    # 创建运行器
    runner = Runner.from_cfg(cfg)

    # 加载权重
    runner.load_checkpoint(args.checkpoint)

    # 执行评估
    runner.test()

    print("评估完成！")
    if args.vis_dir:
        print(f"可视化结果保存在: {args.vis_dir}")

if __name__ == '__main__':
    main()
```

## 评估结果解读

### 输出指标说明

```python
# 典型的评估输出示例
evaluation_results = {
    'scannet': {
        'mAP_0.25': 0.456,  # IoU=0.25时的平均精度
        'mAP_0.5': 0.312,   # IoU=0.5时的平均精度
        'AP_door_0.25': 0.523,     # 门类别在IoU=0.25时的AP
        'AP_door_0.5': 0.387,      # 门类别在IoU=0.5时的AP
        'AP_window_0.25': 0.445,   # 窗类别在IoU=0.25时的AP
        'AP_window_0.5': 0.298,    # 窗类别在IoU=0.5时的AP
        'AR_0.25': 0.512,          # IoU=0.25时的平均召回率
        'AR_0.5': 0.378,           # IoU=0.5时的平均召回率
    },
    's3dis': {
        'mAP_0.25': 0.398,
        'mAP_0.5': 0.267,
        # ... 其他指标
    }
}
```

### 性能基准参考

```python
# 不同数据集的性能基准（仅供参考）
performance_benchmarks = {
    'scannet': {
        'excellent': {'mAP_0.25': '>0.50', 'mAP_0.5': '>0.35'},
        'good': {'mAP_0.25': '0.40-0.50', 'mAP_0.5': '0.25-0.35'},
        'acceptable': {'mAP_0.25': '0.30-0.40', 'mAP_0.5': '0.15-0.25'},
        'poor': {'mAP_0.25': '<0.30', 'mAP_0.5': '<0.15'}
    },
    's3dis': {
        'excellent': {'mAP_0.25': '>0.45', 'mAP_0.5': '>0.30'},
        'good': {'mAP_0.25': '0.35-0.45', 'mAP_0.5': '0.20-0.30'},
        'acceptable': {'mAP_0.25': '0.25-0.35', 'mAP_0.5': '0.10-0.20'},
        'poor': {'mAP_0.25': '<0.25', 'mAP_0.5': '<0.10'}
    }
}
```

## 总结

本文档详细介绍了UniDet3D项目的评估指标体系，涵盖了以下核心内容：

### 主要特点

1. **统一评估框架**：支持多个室内场景数据集的统一评估
2. **灵活的IoU计算**：支持轴对齐和旋转包围盒的IoU计算
3. **完整的评估流程**：从数据预处理到最终指标计算的完整流程
4. **可视化支持**：提供.obj格式输出，便于在MeshLab中查看结果
5. **性能优化**：提供多种优化策略以提高评估效率

### 核心组件

- **数据格式转换**：从语义/实例分割到3D包围盒的自动转换
- **IoU计算引擎**：高效的3D包围盒IoU计算实现
- **评估指标计算**：标准的mAP、AP、AR等指标计算
- **结果可视化**：支持点云和包围盒的可视化输出

### 使用建议

1. **数据集选择**：根据应用场景选择合适的数据集和评估配置
2. **参数调优**：合理设置IoU阈值、置信度阈值等参数
3. **性能优化**：对于大规模评估，建议使用GPU加速和内存优化策略
4. **结果分析**：结合可视化结果进行深入的错误分析

### 扩展性

UniDet3D的评估系统具有良好的扩展性，可以：
- 添加新的数据集支持
- 实现自定义的评估指标
- 集成新的IoU计算方法
- 扩展可视化功能

**相关文件**：
- `unidet3d/indoor_eval.py` - 评估算法实现
- `unidet3d/indoor_metric.py` - 评估指标封装
- `configs/unidet3d_*.py` - 模型和评估配置
- `unidet3d/show_results.py` - 结果可视化
- `unidet3d/axis_aligned_iou_loss.py` - IoU计算实现
- `unidet3d/rotated_iou_loss.py` - 旋转IoU计算
- `tools/test.py` - 标准评估脚本
