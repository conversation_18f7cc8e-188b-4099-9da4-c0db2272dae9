# 数据格式转换需求文档

## 项目概述

本文档描述了将室内点云门窗检测数据集从JSON标注格式转换为PLY点云格式的详细需求。转换后的PLY文件将用于后续的可视化和分析工作。

## 目标

将标注结果JSON文件和RGB LAS文件转换为包含完整信息的PLY格式点云文件，支持：
- XYZ坐标信息
- RGB颜色信息  
- 强度(intensity)信息
- 语义标签信息
- 实例标签信息（仅门窗类别）

## 数据路径和文件结构

### 输入数据路径

1. **标注结果JSON文件路径（新版本命名）**
   - 路径：`/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/`
   - 结构：参考 `/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/03_done_data_file_structure.txt`
   - 每个文件名文件夹下包含 `pcd/` 子文件夹
   - `pcd/` 文件夹内包含一个或多个JSON文件

2. **RGB和强度LAS文件路径（新版本命名）**
   - 路径：`/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/RS10/{batch_name}/{scene_name}/Las/`
   - 包含RGB颜色和强度信息的LAS文件
   - 实际路径结构：`/home/<USER>/01_3D-FAVP/01_semantic/04_rgb_data/RS10/RS10_Batch_02/000_cuishanlantian_res-uf_RS10/Las/20250607120817034.las`

3. **矢量标注floorplan.json文件路径（旧版本命名）**
   - 路径：`/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/`
   - 结构：`{scene_name}/Annotations/floorplan.json`
   - 包含房间边界框信息

### 输出数据路径

- **PLY文件输出路径**：`data/hc3d/ply/` （默认路径，可通过参数指定）
- **日志文件路径**：`tools/data_preprocess/data_preprocess_log/converter_json2ply_log_YYYYMMDD.log`

## 文件命名转换规则

### 新版本到旧版本文件名转换

- **新版本格式**：`000_biguiyuanxingzuan_res-ff_RS10`
- **旧版本格式**：`biguiyuanxingzuan_res_ff_RS10_0`
- **转换规则**：
  - 移除前缀数字和下划线
  - 将连字符替换为下划线
  - 提取房屋编号作为后缀

## 数据处理流程

### 1. JSON标注数据读取

**输入**：
- 文件列表：从 `03_done_data_file_structure.txt` 读取
- JSON文件：每个场景的 `pcd/*.json` 文件

**处理逻辑**：
- 读取同一场景的所有JSON文件
- 提取点云坐标（XYZ）
- 提取语义标签信息
- 提取实例标签信息（仅门窗类别）
- 实例标签重编号：`i*1000 + 原始实例标签`（i为JSON文件索引）

**输出**：
- 点云坐标数组：`(N, 3)`
- 语义标签数组：`(N,)`
- 实例标签数组：`(N,)`

### 2. RGB和强度数据获取

**输入**：
- RGB LAS文件路径（新版本命名）
- JSON点云坐标

**处理逻辑**：
- 加载RGB LAS文件
- 使用KDTree进行点云匹配
- 提取RGB颜色信息
- 提取强度信息
- 合并到JSON点云数据

**输出**：
- RGB颜色数组：`(N, 3)`
- 强度数组：`(N,)`

### 3. 房间包围框过滤

**输入**：
- 新版本文件名
- 旧版本文件名（通过转换获得）
- floorplan.json文件

**处理逻辑**：
- 根据文件名转换规则获取旧版本文件名
- 读取对应的 `floorplan.json` 文件
- 提取 `boundingBox` 信息
- 过滤包围框外的点云数据

**输出**：
- 过滤后的点云数据

### 4. PLY格式输出

**输入**：
- 过滤后的点云坐标：`(N, 3)`
- RGB颜色信息：`(N, 3)`
- 强度信息：`(N,)`
- 语义标签：`(N,)`
- 实例标签：`(N,)`

**处理逻辑**：
- 构建PLY文件头部
- 写入点云属性定义
- 保存点云数据

**输出格式**：
```
property float x
property float y  
property float z
property uchar red
property uchar green
property uchar blue
property float intensity
property int semantic_label
property int instance_label
```

## 技术规格

### 数据类型规范

- **坐标**：float32
- **RGB颜色**：uint8 (0-255)
- **强度**：float32
- **语义标签**：int32
- **实例标签**：int32

### 实例标签处理规则

- **门窗类别**：保留原始实例标签，按 `i*1000 + 原始标签` 重编号
- **其他类别**：设置为背景（标签值0）
- **背景点**：标签值0

### 容错机制

- **缺失文件**：记录警告，跳过处理
- **数据不匹配**：记录错误信息，继续处理其他文件
- **格式错误**：详细记录错误位置和原因

## 性能要求

- **内存优化**：使用流式处理，避免同时加载大量数据
- **并行处理**：支持多进程处理不同场景
- **进度监控**：提供处理进度反馈

## 日志和监控

### 日志级别

- **INFO**：正常处理信息
- **WARNING**：非致命错误和警告
- **ERROR**：处理错误和异常

### 日志内容

- 文件处理状态
- 数据统计信息
- 错误和异常详情
- 处理时间统计

## 质量保证

### 数据验证

- 点云数量一致性检查
- 坐标范围合理性验证
- 标签值有效性检查

### 输出验证

- PLY文件格式正确性
- 数据完整性验证
- 可视化验证支持

## 依赖项

### Python包依赖

- numpy：数值计算
- json：JSON文件处理
- laspy：LAS文件读取
- open3d：点云处理（可选）
- scipy：空间数据结构（KDTree）
- logging：日志系统

### 外部文件依赖

- `filename_converter.py`：文件名转换工具（用于floorplan.json路径转换）
- `process2_vis_las.py`：参考实现（批次处理逻辑）

注意：脚本不再依赖 `03_done_data_file_structure.txt` 文件，而是直接扫描指定批次目录获取场景列表。

## 使用示例

### 基本使用

```bash
# 基本转换（使用默认路径，输出到 data/hc3d/ply）
python tools/data_preprocess/converter_json2ply.py

# 指定自定义路径和批次
python tools/data_preprocess/converter_json2ply.py \
    --json_data_root /path/to/json/data \
    --rgb_data_root /path/to/rgb/data \
    --floorplan_data_root /path/to/floorplan/data \
    --batch_name_list RS10_Batch_02 RS10_Batch_03 \
    --output_root ./output/ply_files
```

### 高级使用

```bash
# 处理特定场景
python tools/data_preprocess/converter_json2ply.py \
    --scene_names "000_scene1_res-ff_RS10" "001_scene2_res-uf_RS10"

# 测试单个场景
python tools/data_preprocess/converter_json2ply.py \
    --max_scenes 1

# 多进程批量处理（最多64线程）
python tools/data_preprocess/converter_json2ply.py \
    --num_workers 32 \
    --max_scenes 100

# 自定义实例类别和批次
python tools/data_preprocess/converter_json2ply.py \
    --target_categories door window openings \
    --batch_name_list RS10_Batch_02 RS10_Batch_03

# 仅保存坐标和标签信息（跳过RGB和intensity）
python tools/data_preprocess/converter_json2ply.py \
    --no_rgb --no_intensity

# 仅保存RGB，跳过intensity
python tools/data_preprocess/converter_json2ply.py \
    --no_intensity
```

### 测试和调试

```bash
# 运行测试套件
python tools/debug/test_converter_json2ply.py \
    --output_root ./test_output \
    --max_scenes 3

# 测试特定场景
python tools/debug/test_converter_json2ply.py \
    --scene_name "000_scene_name_RS10" \
    --output_root ./test_output

# 验证PLY文件
python tools/debug/test_converter_json2ply.py \
    --validate_ply ./output/scene.ply
```

### 查看使用示例

```bash
# 运行示例脚本
python tools/data_preprocess/example_usage.py
```

## 输出格式

生成的PLY文件包含以下属性（可选）：

### 完整模式（默认）
- **坐标**: x, y, z (float)
- **颜色**: red, green, blue (0-255) - 可选
- **强度**: intensity (float) - 可选
- **语义标签**: semantic_label (int)
  - 0: wall, 1: floor, 2: ceiling, 3: door, 4: window, 5: other
- **实例标签**: instance_label (int)
  - 门窗实例: 唯一ID (file_index * 1000 + original_id)
  - 其他类别: 0 (背景)

### 精简模式（--no_rgb --no_intensity）
- **坐标**: x, y, z (float)
- **语义标签**: semantic_label (int)
- **实例标签**: instance_label (int)

### 控制参数
- `--save_rgb` / `--no_rgb`: 控制是否保存RGB颜色信息
- `--save_intensity` / `--no_intensity`: 控制是否保存强度信息

## 日志和监控

- 日志文件位置: `tools/data_preprocess/data_preprocess_log/converter_json2ply_log_YYYYMMDD.log`
- 包含详细的处理信息、错误记录和统计数据
- 支持多级别日志记录 (INFO, WARNING, ERROR)

## 故障排除

### 常见问题

1. **文件路径不存在**
   - 检查输入路径是否正确
   - 确认文件权限

2. **点云匹配失败**
   - 调整tolerance参数
   - 检查坐标系统一致性

3. **内存不足**
   - 减少num_workers数量
   - 使用较小的batch_size

4. **文件名转换失败**
   - 检查文件名格式是否符合预期
   - 查看日志中的详细错误信息

### 性能优化

- 使用SSD存储提高I/O性能
- 根据CPU核心数调整num_workers
- 对于大数据集，考虑分批处理

## 后续扩展

- 支持其他点云格式输出 (PCD, XYZ)
- 增加数据质量评估功能
- 支持增量处理模式
- 集成可视化验证工具
- 支持GPU加速点云匹配
