import torch
import torch.nn.functional as F
import spconv.pytorch as spconv
from torch_scatter import scatter_mean
import MinkowskiEngine as ME
from torch import Tensor

from mmcv.ops import nms3d, nms3d_normal
import warnings
from mmengine.structures import InstanceData

from mmdet3d.registry import MODELS
from mmdet3d.structures import DepthInstance3DBoxes
from mmdet3d.models import Base3DDetector
from mmdet3d.models.layers.box3d_nms import aligned_3d_nms
from mmdet3d.structures import rotation_3d_in_axis

from .criterion import _bbox_to_loss
from .structures import InstanceData_

def python_nms3d(bboxes, scores, iou_thr):
    """Pure Python implementation of 3D NMS as final fallback."""
    import torch
    import numpy as np

    if len(bboxes) == 0:
        return torch.empty((0,), dtype=torch.long, device=bboxes.device)

    # Convert to numpy for easier computation
    device = bboxes.device
    bboxes_np = bboxes.cpu().numpy()
    scores_np = scores.cpu().numpy()

    # Sort by scores in descending order
    order = scores_np.argsort()[::-1]

    keep = []
    while len(order) > 0:
        # Pick the bbox with highest score
        i = order[0]
        keep.append(i)

        if len(order) == 1:
            break

        # Compute IoU with remaining bboxes
        # Simple 3D IoU approximation using axis-aligned bounding boxes
        bbox_i = bboxes_np[i]
        remaining_bboxes = bboxes_np[order[1:]]

        # Extract center and size (assuming format: [x, y, z, dx, dy, dz, ...])
        center_i = bbox_i[:3]
        size_i = bbox_i[3:6]

        centers_remaining = remaining_bboxes[:, :3]
        sizes_remaining = remaining_bboxes[:, 3:6]

        # Compute axis-aligned IoU
        min_i = center_i - size_i / 2
        max_i = center_i + size_i / 2

        mins_remaining = centers_remaining - sizes_remaining / 2
        maxs_remaining = centers_remaining + sizes_remaining / 2

        # Intersection
        inter_mins = np.maximum(min_i, mins_remaining)
        inter_maxs = np.minimum(max_i, maxs_remaining)
        inter_sizes = np.maximum(0, inter_maxs - inter_mins)
        inter_volumes = np.prod(inter_sizes, axis=1)

        # Union
        volume_i = np.prod(size_i)
        volumes_remaining = np.prod(sizes_remaining, axis=1)
        union_volumes = volume_i + volumes_remaining - inter_volumes

        # IoU
        ious = inter_volumes / (union_volumes + 1e-8)

        # Keep bboxes with IoU < threshold
        inds = np.where(ious <= iou_thr)[0]
        order = order[inds + 1]  # +1 because we removed the first element

    return torch.tensor(keep, dtype=torch.long, device=device)

def safe_nms3d(bboxes, scores, iou_thr):
    """Safe NMS3D with multiple fallback strategies."""
    try:
        # Try CUDA version first
        return nms3d(bboxes, scores, iou_thr)
    except RuntimeError as e:
        if "implementation for device cuda" in str(e) or "implementation for device cpu" in str(e):
            warnings.warn(
                f"MMCV NMS3D failed ({str(e)}), falling back to pure Python implementation. "
                "This may be slower but should work correctly.",
                UserWarning
            )
            # Use pure Python implementation
            return python_nms3d(bboxes, scores, iou_thr)
        else:
            raise e

def safe_nms3d_normal(bboxes, scores, iou_thr):
    """Safe NMS3D normal with comprehensive fallback mechanism."""
    try:
        # Try CUDA version first
        return nms3d_normal(bboxes, scores, iou_thr)
    except RuntimeError as e:
        if "implementation for device" in str(e):
            warnings.warn(
                f"CUDA NMS3D normal failed ({str(e)}), falling back to CPU version. "
                "This may be slower but should work correctly.",
                UserWarning
            )
            # Try CPU version
            device = bboxes.device
            cpu_bboxes = bboxes.cpu()
            cpu_scores = scores.cpu()
            try:
                nms_ids = nms3d_normal(cpu_bboxes, cpu_scores, iou_thr)
                return nms_ids.to(device) if hasattr(nms_ids, 'to') else nms_ids
            except RuntimeError as cpu_e:
                if "implementation for device" in str(cpu_e):
                    warnings.warn(
                        f"CPU NMS3D normal also failed ({str(cpu_e)}), falling back to pure Python implementation. "
                        "This will be slower but should work correctly.",
                        UserWarning
                    )
                    # Final fallback to pure Python implementation
                    nms_ids = python_nms3d(cpu_bboxes, cpu_scores, iou_thr)
                    return nms_ids.to(device) if hasattr(nms_ids, 'to') else nms_ids
                else:
                    raise cpu_e
        else:
            raise e

@MODELS.register_module()
class UniDet3D(Base3DDetector):
    r"""UniDet3D for unifed 3D object detection.

    Args:
        in_channels (int): Number of input channels.
        num_channels (int): Number of output channels.
        voxel_size (float): Voxel size.
        min_spatial_shape (int): Minimal shape for spconv tensor.
        query_thr (float): We select min(query_thr, n_queries) queries
            for training and testing.
        use_superpoints (bool): Flag to indicate whether to use superpoints
            for improved detection.
        bbox_by_mask (bool): Whether to derive bounding boxes from masks.
        target_by_distance (bool): Whether to use targets based on distance 
            to bbox center.
        fast_nms (bool): Flag for using fast Non-Maximum Suppression.
        use_sync_bn (bool, optional): Flag to use synchronized 
            batch normalization. Defaults to True.
        backbone (ConfigDict, optional): Config dict of the backbone. 
            Defaults to None.
        decoder (ConfigDict, optional): Config dict of the decoder. 
            Defaults to None.
        criterion (ConfigDict, optional): Config dict of the criterion. 
            Defaults to None.
        train_cfg (dict, optional): Config dict of training hyper-parameters.
            Defaults to None.
        test_cfg (dict, optional): Config dict of test hyper-parameters. 
            Defaults to None.
        data_preprocessor (dict or ConfigDict, optional): The pre-process 
            config of :class:BaseDataPreprocessor.
            It usually includes:
                - ``pad_size_divisor``
                - ``pad_value``
                - ``mean``
                - ``std``.
        init_cfg (dict or ConfigDict, optional): The config to control the 
            initialization. Defaults to None.
    """
    def __init__(self,
                 in_channels,
                 num_channels,
                 voxel_size,
                 min_spatial_shape,
                 query_thr,
                 use_superpoints,
                 bbox_by_mask, 
                 target_by_distance,
                 fast_nms,
                 use_sync_bn=True,
                 backbone=None,
                 decoder=None,
                 criterion=None,
                 train_cfg=None,
                 test_cfg=None,
                 data_preprocessor=None,
                 init_cfg=None):
        super(Base3DDetector, self).__init__(
            data_preprocessor=data_preprocessor, init_cfg=init_cfg)
        if backbone is not None:
            self.unet = MODELS.build(backbone)
        self.decoder = MODELS.build(decoder)
        self.criterion = MODELS.build(criterion)
        self.voxel_size = voxel_size
        self.min_spatial_shape = min_spatial_shape
        self.query_thr = query_thr
        self.use_superpoints = use_superpoints
        self.bbox_by_mask = bbox_by_mask
        self.target_by_distance = target_by_distance 
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg
        self.use_sync_bn = use_sync_bn
        self.fast_nms = fast_nms
        self._init_layers(in_channels, num_channels)
    
    def _init_layers(self, in_channels, num_channels):
        self.input_conv = spconv.SparseSequential(
            spconv.SubMConv3d(
                in_channels,
                num_channels,
                kernel_size=3,
                padding=1,
                bias=False,
                indice_key='subm1'))
        if self.use_sync_bn:
            self.output_layer = spconv.SparseSequential(
                torch.nn.SyncBatchNorm(num_channels, eps=1e-4, momentum=0.1),
                torch.nn.ReLU(inplace=True))
        else:
            self.output_layer = spconv.SparseSequential(
                torch.nn.BatchNorm1d(num_channels, eps=1e-4, momentum=0.1),
                torch.nn.ReLU(inplace=True))

    def extract_feat(self, x, superpoints, inverse_mapping, batch_offsets):
        """Extract features from sparse tensor.

        Args:
            x (SparseTensor): Input sparse tensor of shape
                (n_points, in_channels).
            superpoints (Tensor): of shape (n_points,).
            inverse_mapping (Tesnor): of shape (n_points,).
            batch_offsets (List[int]): of len batch_size + 1.

        Returns:
            List[Tensor]: of len batch_size,
                each of shape (n_points_i, n_channels).
        """
        x = self.input_conv(x)
        x, _ = self.unet(x)
        x = self.output_layer(x)
        x = scatter_mean(x.features[inverse_mapping], superpoints, dim=0)
        out = []
        for i in range(len(batch_offsets) - 1):
            out.append(x[batch_offsets[i]: batch_offsets[i + 1]])
        return out

    def collate(self, points, elastic_points=None):
        """Collate a batch of points into a sparse tensor.

        Args:
            points (List[Tensor]): A batch of point tensors. Each tensor
                should contain points in the format (N, 3 + num_features),
                where N is the number of points.
            elastic_points (List[Tensor], optional): A batch of transformed
                point tensors (if any) after elastic point augmentation. 
                Defaults to None.

        Returns:
            Tuple[Tensor, Tensor, Tensor, Tensor]: 
                - coordinates (Tensor): The sparse tensor coordinates after 
                quantization and normalization.
                - features (Tensor): The features corresponding to the points.
                - inverse_mapping (Tensor): A mapping of points to their 
                indices in the original tensor.
                - spatial_shape (Tensor): The spatial shape of the sparse tensor,
                clipped to the minimum spatial shape.
        """
        if elastic_points is None:
            coordinates, features = ME.utils.batch_sparse_collate(
                [((p[:, :3] - p[:, :3].min(0)[0]) / self.voxel_size,
                  torch.hstack((p[:, 3:], p[:, :3] - p[:, :3].mean(0))))
                 for p in points])
        else:
            coordinates, features = ME.utils.batch_sparse_collate(
                [((el_p - el_p.min(0)[0]),
                  torch.hstack((p[:, 3:], p[:, :3] - p[:, :3].mean(0))))
                 for el_p, p in zip(elastic_points, points)])
        
        spatial_shape = torch.clip(
            coordinates.max(0)[0][1:] + 1, self.min_spatial_shape)
        field = ME.TensorField(features=features, coordinates=coordinates)
        tensor = field.sparse()
        coordinates = tensor.coordinates
        features = tensor.features
        inverse_mapping = field.inverse_mapping(tensor.coordinate_map_key)

        return coordinates, features, inverse_mapping, spatial_shape

    def _forward(*args, **kwargs):
        """Implement abstract method of Base3DDetector."""
        pass

    def _select_queries(self, x, gt_instances):
        """Select queries for the training pass.

        Args:
            x (List[Tensor]): A list of tensors of length `batch_size`, 
                where each tensor has the shape (n_points_i, n_channels).
            gt_instances (List[InstanceData_]): A list of ground truth 
                instances of length `batch_size`, where each instance may 
                contain:
                    - labels of shape (n_gts_i,)
                    - sp_masks of shape (n_gts_i, n_points_i).

        Returns:
            Tuple[List[Tensor], List[Tensor], List[InstanceData_]]:
                - queries (List[Tensor]): A list of queries of length 
                `batch_size`, where each query has the shape 
                (n_queries_i, n_channels).
                - sp_centers (List[Tensor]): A list of tensors representing 
                spatial centers for the selected queries.
                - updated_gt_instances (List[InstanceData_]): A list of ground 
                truth instances (same length as `gt_instances`), 
                each updated with query_masks of shape (n_gts_i, n_queries_i).
        """
        queries = []
        sp_centers = []
        for i in range(len(x)):
            if len(x[i]) > self.query_thr:
                ids = torch.randperm(len(x[i]))[:self.query_thr].to(x[i].device)
                queries.append(x[i][ids])
                sp_centers.append(gt_instances[i].sp_centers[ids])
                gt_instances[i].query_masks = gt_instances[i].sp_masks[:, ids]
                gt_instances[i].sp_centers = gt_instances[i].sp_centers[ids]
            else:
                queries.append(x[i])
                sp_centers.append(gt_instances[i].sp_centers)
                gt_instances[i].query_masks = gt_instances[i].sp_masks
        return queries, sp_centers, gt_instances

    def get_bboxes_by_masks(self, masks, points):
        """Generate 3D bounding boxes from masks.

        Args:
            masks (Tensor): A tensor of boolean masks, of shape 
                (n, n_points) indicating which points belong to each object.
            points (Tensor): A tensor of shape (n_points, 3) representing 
                the 3D coordinates of the points.

        Returns:
            DepthInstance3DBoxes: A set of 3D bounding boxes, where each box 
            is represented as a tensor of shape (6,) containing:
                - Center coordinates (x, y, z)
                - Dimensions (width, height, depth)
            
            If no masks are provided, an empty `DepthInstance3DBoxes` instance 
            will be returned.

        """
        boxes = []
        for mask in masks:
            object_points = points[mask]
            xyz_min = object_points.min(dim=0).values
            xyz_max = object_points.max(dim=0).values
            center = (xyz_max + xyz_min) / 2
            size = xyz_max - xyz_min
            box = torch.cat((center, size))
            boxes.append(box)
        if len(boxes) == 0:
            bboxes = DepthInstance3DBoxes(
                masks.new_zeros(0, 6), with_yaw=False, 
                box_dim=6, origin=(0.5, 0.5, 0.5))
        else:
            boxes = torch.stack(boxes)
            bboxes = DepthInstance3DBoxes(
                boxes, with_yaw=False, box_dim=6, origin=(0.5, 0.5, 0.5))
        return bboxes
    
    def get_gt_inst_masks(self, masks_src):
        """Create ground truth instance masks.
        
        Args:
            mask_src (Tensor): of shape (n_points, 1).
        
        
        Returns:
            mask (Tensor): instance masks of shape (n_points, num_inst_obj).
        """
        masks = masks_src.clone()
        if torch.sum(masks == -1) != 0:
            masks[masks == -1] = torch.max(masks) + 1
            masks = torch.nn.functional.one_hot(masks)[:, :-1]
        else:
            masks = torch.nn.functional.one_hot(masks)

        return masks.bool()

    def loss(self, batch_inputs_dict, batch_data_samples, **kwargs):
        """Calculate losses from a batch of inputs dict and data samples.

        Args:
            batch_inputs_dict (dict): The model input dict which include
                `points` key.
            batch_data_samples (List[:obj:`Det3DDataSample`]): The Data
                Samples. It includes information such as
                `gt_instances_3d`.
        Returns:
            dict: A dictionary of loss components.
        """
        batch_offsets = [0]
        superpoint_bias = 0
        sp_gt_instances = []
        sp_pts_masks = []
        sp_centers = []
        
        if batch_inputs_dict.get('elastic_coords') is not None:
            points = [(point - point.min(0)[0]) * self.voxel_size for point in \
                batch_inputs_dict['elastic_coords']]
            shifts = [point.min(0)[0] * self.voxel_size for point in \
                batch_inputs_dict['elastic_coords']]
        else:
            points = [point[:, :3] - point[:, :3].min(0)[0] for point in \
                batch_inputs_dict['points']]
            shifts = [point[:, :3].min(0)[0] for point in \
                batch_inputs_dict['points']]

        datasets_names = []
        for i in range(len(batch_data_samples)):
            datasets_names.append(self.get_dataset(
                            batch_data_samples[i].lidar_path))
            gt_pts_seg = batch_data_samples[i].gt_pts_seg
            dataset = self.decoder.datasets.index(datasets_names[i])
            if self.bbox_by_mask[dataset]:
                gt_masks = self.get_gt_inst_masks(gt_pts_seg.pts_instance_mask)
                batch_data_samples[i].gt_instances_3d.bboxes_3d = \
                                            self.get_bboxes_by_masks(gt_masks.T,
                                                                    points[i])
            else:
                center = batch_data_samples[i].gt_instances_3d.\
                                    bboxes_3d.gravity_center - \
                                    shifts[i]
                bboxes = torch.cat((center,
                                    batch_data_samples[i].gt_instances_3d.\
                                    bboxes_3d.tensor[:, 3:]),
                                    dim=1)
                batch_data_samples[i].gt_instances_3d.bboxes_3d = \
                    DepthInstance3DBoxes(
                        bboxes, 
                        with_yaw=batch_data_samples[i].gt_instances_3d.\
                                                    bboxes_3d.with_yaw, 
                        box_dim=bboxes.shape[1], origin=(0.5, 0.5, 0.5))
            
            batch_data_samples[i].gt_instances_3d.sp_centers = \
                scatter_mean(points[i], gt_pts_seg.sp_pts_mask, dim=0)
            if self.target_by_distance[dataset]:
                batch_data_samples[i].gt_instances_3d.sp_masks = \
                    self.get_targets(batch_data_samples[i].gt_instances_3d.\
                                        sp_centers,
                                     batch_data_samples[i].gt_instances_3d.\
                                        bboxes_3d,
                                     self.train_cfg.topk)
            sp_centers.append(batch_data_samples[i].gt_instances_3d.sp_centers)
            gt_pts_seg.sp_pts_mask += superpoint_bias
            superpoint_bias = gt_pts_seg.sp_pts_mask.max().item() + 1
            batch_offsets.append(superpoint_bias)

            sp_gt_instances.append(batch_data_samples[i].gt_instances_3d)
            sp_pts_masks.append(gt_pts_seg.sp_pts_mask)

        coordinates, features, inverse_mapping, spatial_shape = self.collate(
            batch_inputs_dict['points'],
            batch_inputs_dict.get('elastic_coords', None))

        x = spconv.SparseConvTensor(
            features, coordinates, spatial_shape, len(batch_data_samples))
        sp_pts_masks = torch.hstack(sp_pts_masks)
        x = self.extract_feat(
            x, sp_pts_masks, inverse_mapping, batch_offsets)

        queries, sp_centers_queries, sp_gt_instances = \
                    self._select_queries(x, sp_gt_instances)
        x = self.decoder(queries, sp_centers_queries, datasets_names)
        loss = self.criterion(x, sp_gt_instances, datasets_names)

        return loss

    def get_dataset(self, lidar_path):
        for dataset in self.decoder.datasets:
            if dataset in lidar_path.split('/'):
                return dataset

    def get_targets(self, points, gt_bboxes, topk):
        """Compute targets for final locations for a single scene.

        Args:
            points (Tensor): Final locations for level.
            gt_bboxes (BaseInstance3DBoxes): Ground truth boxes.
            topk (int): The number of nearest ground truth boxes 
                to consider for target assignment.

        Returns:
            Tensor: A tensor indicating which ground truth boxes each 
                point is assigned to, where the shape is (n_points, n_boxes).        
        """
        float_max = points[0].new_tensor(1e8)
        n_points = len(points)
        n_boxes = len(gt_bboxes)
        boxes = torch.cat((gt_bboxes.gravity_center, 
                           gt_bboxes.tensor[:, 3:]),
                          dim=1)
        boxes = boxes.expand(n_points, n_boxes, boxes.shape[1])
        points = points.unsqueeze(1).expand(n_points, n_boxes, 3)

        center = boxes[..., :3]
        center_distances = torch.sum(torch.pow(center - points, 2), dim=-1)

        topk_distances = torch.topk(
            center_distances,
            min(topk + 1, len(center_distances)),
            largest=False,
            dim=0).values[-1]
        topk_condition = center_distances < topk_distances.unsqueeze(0)
        center_distances = torch.where(topk_condition, center_distances,
                                        float_max)
        min_values, min_ids = center_distances.min(dim=1)
        min_inds = torch.where(min_values < float_max, min_ids, n_boxes)
        min_dist_condition = torch.nn.functional.one_hot(
            min_inds, num_classes=n_boxes + 1)[:, :-1].bool()

        return min_dist_condition.T

    def predict(self, batch_inputs_dict, batch_data_samples, **kwargs):
        """Predict results from a batch of inputs and data samples
                with post-processing.

        Args:
            batch_inputs_dict (dict): A dictionary containing model inputs, 
                which must include 'points' key.
            batch_data_samples (List[:obj:Det3DDataSample]): A list of Data 
                Samples. Each Data Sample includes information such as
                superpoints (gt_pts_seg.sp_pts_mask).

        Returns:
            List[:obj:Det3DDataSample]: Detection results for the input 
                samples. Each Det3DDataSample contains 'pred_instances_3d' 
                with the following keys:
                    - bboxes_3d (Tensor): 3D bounding boxes of detected 
                    instances, shape (num_instances, 6).
                    - scores_3d (Tensor): Classification scores for each 
                    detected instance, shape (num_instances,).
                    - labels_3d (Tensor): Labels of instances, shape 
                    (num_instances,).
        """
        batch_offsets = [0]
        superpoint_bias = 0
        sp_pts_masks = []
        sp_centers = []
        datasets_names = []
        sp_pts_masks_src = []
        points_src = []
        for i in range(len(batch_data_samples)):
            datasets_names.append(self.get_dataset(
                            batch_data_samples[i].lidar_path))
            gt_pts_seg = batch_data_samples[i].gt_pts_seg
            points = batch_inputs_dict['points'][i][:, :3]
            points_src.append(points)
            sp_centers.append(scatter_mean(points, 
                                           gt_pts_seg.sp_pts_mask, dim=0))
            sp_pts_masks_src.append(gt_pts_seg.sp_pts_mask)
            gt_pts_seg.sp_pts_mask += superpoint_bias
            superpoint_bias = gt_pts_seg.sp_pts_mask.max().item() + 1
            batch_offsets.append(superpoint_bias)
            sp_pts_masks.append(gt_pts_seg.sp_pts_mask)

        coordinates, features, inverse_mapping, spatial_shape = self.collate(
            batch_inputs_dict['points'])
        x = spconv.SparseConvTensor(
            features, coordinates, spatial_shape, len(batch_data_samples))
        sp_pts_masks = torch.hstack(sp_pts_masks)
        x = self.extract_feat(
            x, sp_pts_masks, inverse_mapping, batch_offsets)

        x = self.decoder(x, sp_centers, datasets_names)

        results_list = self.predict_by_feat(x, sp_pts_masks_src, 
                                            points_src, datasets_names)
        
        for i, data_sample in enumerate(batch_data_samples):
            bboxes, labels, scores = results_list[i]
            data_sample.pred_instances_3d = InstanceData_(
                bboxes_3d=bboxes, scores_3d=scores, labels_3d=labels,
                points=batch_inputs_dict['points'][0])
        
        return batch_data_samples

    def predict_by_feat(self, out, sp_pts_masks, points, 
                        datasets_names):
        """Predict bounding boxes and labels from model outputs.

        Args:
            out (dict): A dictionary containing model outputs with the 
                following keys:
                - 'cls_preds': Tensor of shape (n_bboxes, num_classes) 
                containing classification scores for each point.
                - 'bboxes': Tensor of shape (n_bboxes, 7) containing 
                predicted bounding boxes.
            sp_pts_masks (List[Tensor]): A list of superpoint masks.
            points (List[Tensor]): A list of point tensors containing 
                the 3D coordinates of the points being evaluated.

            datasets_names (List[str]): A list of dataset names 
                corresponding to the input samples.

        Returns:
            List[Tuple[DepthInstance3DBoxes, Tensor, Tensor]]: A list containing 
            tuples of predicted bounding boxes and their associated 
            labels and scores.
        """
        cls_preds = out['cls_preds'][0]
        pred_bboxes = out['bboxes'][0]
        sp_pts_mask = sp_pts_masks[0] 
        point = points[0]
        dataset_name = datasets_names[0]
    
        scores = F.softmax(cls_preds, dim=-1)[:, :-1]
        num_classes = scores.shape[1]
        labels = torch.arange(
            num_classes,
            device=scores.device).unsqueeze(0).repeat(
                len(cls_preds), 1).flatten(0, 1)
        scores, topk_idx = scores.flatten(0, 1).topk(
            self.test_cfg.topk_insts, sorted=True)
        labels = labels[topk_idx]

        topk_idx = torch.div(topk_idx, num_classes, rounding_mode='floor')
        pred_bboxes = pred_bboxes[topk_idx]

        fast_nms = self.fast_nms[self.decoder.\
                                        datasets.index(dataset_name)]
        iou_thr = self.test_cfg.iou_thr[self.decoder.\
                                        datasets.index(dataset_name)]
        nms_bboxes, nms_scores, nms_labels = \
                self._single_scene_multiclass_nms(pred_bboxes,
                                                  scores, 
                                                  labels,
                                                  fast_nms, 
                                                  iou_thr)
        if not self.use_superpoints[
            self.decoder.datasets.index(dataset_name)]:
            return [(DepthInstance3DBoxes(
                nms_bboxes, 
                with_yaw=nms_bboxes.shape[1] == 7, 
                box_dim=nms_bboxes.shape[1], 
                origin=(0.5, 0.5, 0.5)),
                nms_labels, nms_scores)]
        else:
            return self.trim_bboxes_by_superpoints(sp_pts_mask, point, 
                                                   nms_bboxes, nms_labels,
                                                   nms_scores)

    def trim_bboxes_by_superpoints(self, sp_pts_mask, point, 
                                   bboxes, labels, scores):
        """Trim bounding boxes based on superpoint masks.

        Args:
            sp_pts_mask (Tensor): A boolean tensor indicating the valid points 
                for each superpoint.
            point (Tensor): A tensor of shape (n_points, 3) representing the 
                3D coordinates of the points.
            bboxes (Tensor): A tensor of predicted bounding boxes, with shape 
                (n_boxes, 6) or (n_boxes, 7) if yaw is included.
            labels (Tensor): A tensor of shape (n_boxes,) containing the 
                predicted labels for each bounding box.
            scores (Tensor): A tensor of shape (n_boxes,) containing the 
                classification scores for each bounding box.

        Returns:
            List[Tuple[DepthInstance3DBoxes, Tensor, Tensor]]: A list 
                containing a tuple of trimmed bounding boxes, 
                labels, and scores.
        """
        n_points = point.shape[0]
        n_boxes = bboxes.shape[0]
        point = point.unsqueeze(1).expand(n_points, n_boxes, 3)
        if bboxes.shape[1] == 6:
            bboxes = torch.cat(
                (bboxes, torch.zeros_like(bboxes[:, :1])),
                dim=1)
        bboxes = bboxes.unsqueeze(0).expand(n_points, n_boxes, 
                                            bboxes.shape[1])
        face_distances = get_face_distances(point, bboxes)

        inside_bbox = face_distances.min(dim=-1).values > 0
        inside_bbox = inside_bbox.T
        sp_inside = scatter_mean(inside_bbox.float(), 
                                        sp_pts_mask, dim=-1)
        sp_del = sp_inside < self.test_cfg.low_sp_thr
        inside_bbox[sp_del[:, sp_pts_mask]] = False

        sp_add = sp_inside > self.test_cfg.up_sp_thr
        inside_bbox[sp_add[:, sp_pts_mask]] = True

        points_for_max = point.clone()
        points_for_min = point.clone()
        points_for_max[~inside_bbox.T.bool()] = float('-inf')
        points_for_min[~inside_bbox.T.bool()] = float('inf')
        bboxes_max = points_for_max.max(axis=0)[0]
        bboxes_min = points_for_min.min(axis=0)[0]
        bboxes_sizes = bboxes_max - bboxes_min
        bboxes_centers = (bboxes_max + bboxes_min) / 2
        bboxes = torch.hstack((bboxes_centers, bboxes_sizes))
        bboxes = DepthInstance3DBoxes(bboxes, with_yaw=False, 
                                      box_dim=6, origin=(0.5, 0.5, 0.5))       
        return [(bboxes, labels, scores)]

    def _single_scene_multiclass_nms(self, bboxes, scores, 
                                     labels, fast_nms, iou_thr):
        """Multi-class nms for a single scene.

        Args:
            bboxes (Tensor): Predicted bounding boxes of shape (N_boxes, 6) 
                or (N_boxes, 7), where each box represents (x, y, z, length, 
                width, height) and optionally yaw.
            scores (Tensor): Predicted scores for the bounding boxes of 
                shape (N_boxes,), representing confidence scores.
            labels (Tensor): Predicted labels for each bounding box, 
                shape (N_boxes,).
            fast_nms (bool): Flag indicating whether to use the fast NMS 
                implementation.
            iou_thr (float): IoU threshold for NMS to filter overlapping boxes.

        Returns:
            tuple[Tensor, ...]: Predicted bboxes, scores and labels.
        """
        classes = labels.unique()
        with_yaw = bboxes.shape[1] == 7
        nms_bboxes, nms_scores, nms_labels = [], [], []
        for class_id in classes:
            ids = scores[labels == class_id] > self.test_cfg.score_thr
            if not ids.any():
                continue

            class_scores = scores[labels == class_id][ids]
            class_bboxes = bboxes[labels == class_id][ids]
            class_labels = labels[labels == class_id][ids]
            if with_yaw:
                nms_ids = safe_nms3d(class_bboxes, class_scores, iou_thr)
            else:
                if fast_nms:
                    class_bboxes = torch.cat(
                        (class_bboxes, torch.zeros_like(class_bboxes[:, :1])),
                        dim=1)
                    nms_ids = safe_nms3d_normal(class_bboxes, class_scores, iou_thr)
                else:
                    nms_ids = aligned_3d_nms(_bbox_to_loss(class_bboxes),
                                class_scores, class_labels, iou_thr)

            nms_bboxes.append(class_bboxes[nms_ids])
            nms_scores.append(class_scores[nms_ids])
            nms_labels.append(class_labels[nms_ids])

        if len(nms_bboxes):
            nms_bboxes = torch.cat(nms_bboxes, dim=0)
            nms_scores = torch.cat(nms_scores, dim=0)
            nms_labels = torch.cat(nms_labels, dim=0)
        else:
            nms_bboxes = bboxes.new_zeros((0, bboxes.shape[1]))
            nms_scores = bboxes.new_zeros((0, ))
            nms_labels = bboxes.new_zeros((0, ))

        return nms_bboxes, nms_scores, nms_labels

def get_face_distances(points: Tensor, boxes: Tensor) -> Tensor:
    """Calculate distances from point to box faces.

    Args:
        points (Tensor): Final locations of shape (N_points, N_boxes, 3).
        boxes (Tensor): 3D boxes of shape (N_points, N_boxes, 7)

    Returns:
        Tensor: Face distances of shape (N_points, N_boxes, 6),
        (dx_min, dx_max, dy_min, dy_max, dz_min, dz_max).
    """
    shift = torch.stack(
        (points[..., 0] - boxes[..., 0], points[..., 1] - boxes[..., 1],
            points[..., 2] - boxes[..., 2]),
        dim=-1).permute(1, 0, 2)
    shift = rotation_3d_in_axis(
        shift, -boxes[0, :, 6], axis=2).permute(1, 0, 2)
    centers = boxes[..., :3] + shift
    dx_min = centers[..., 0] - boxes[..., 0] + boxes[..., 3] / 2
    dx_max = boxes[..., 0] + boxes[..., 3] / 2 - centers[..., 0]
    dy_min = centers[..., 1] - boxes[..., 1] + boxes[..., 4] / 2
    dy_max = boxes[..., 1] + boxes[..., 4] / 2 - centers[..., 1]
    dz_min = centers[..., 2] - boxes[..., 2] + boxes[..., 5] / 2
    dz_max = boxes[..., 2] + boxes[..., 5] / 2 - centers[..., 2]
    return torch.stack((dx_min, dx_max, dy_min, dy_max, dz_min, dz_max),
                        dim=-1)