#!/bin/bash

# UniDet3D Dataset Setup Script
# This script extracts and properly organizes all six datasets for UniDet3D

set -e  # Exit on any error

echo "=== UniDet3D Dataset Setup ==="
echo "Starting dataset extraction and preparation..."

# Define paths
DATA_SOURCE="/home/<USER>/data/unidet3d"
DATA_TARGET="$(dirname "$0")/../data"  # 相对于scripts目录的上级目录
PROJECT_ROOT="$(dirname "$0")/.."      # 项目根目录

# Create data directory if it doesn't exist
mkdir -p "$DATA_TARGET"

# Function to extract and organize dataset
extract_dataset() {
    local dataset_name=$1
    local tar_file="$DATA_SOURCE/${dataset_name}.tar.gz"

    echo "Processing $dataset_name..."

    if [ ! -f "$tar_file" ]; then
        echo "  - Warning: ${dataset_name}.tar.gz not found in $DATA_SOURCE"
        return
    fi

    echo "  - Extracting ${dataset_name}.tar.gz..."

    # Create target dataset directory
    mkdir -p "$DATA_TARGET/$dataset_name"

    # Extract directly to the dataset directory
    echo "  - Extracting to $DATA_TARGET/$dataset_name..."
    tar -xzf "$tar_file" -C "$DATA_TARGET/$dataset_name"

    echo "  - ${dataset_name} extracted successfully"
}

# List of datasets to process
DATASETS=("scannet" "s3dis" "multiscan" "3rscan" "scannetpp" "arkitscenes")

echo "Extracting and organizing datasets..."
for dataset in "${DATASETS[@]}"; do
    extract_dataset "$dataset"
done

echo ""
echo "=== Dataset Organization Complete ==="
echo "Verifying dataset structure..."

# Verify each dataset directory exists and show basic info
for dataset in "${DATASETS[@]}"; do
    if [ -d "$DATA_TARGET/$dataset" ]; then
        echo "✓ $dataset: $(du -sh $DATA_TARGET/$dataset 2>/dev/null | cut -f1 || echo 'N/A')"

        # Show key subdirectories for each dataset
        echo "  Key dirs: $(ls -d $DATA_TARGET/$dataset/*/ 2>/dev/null | xargs -n1 basename | tr '\n' ' ' || echo 'none')"

        # Check for PKL files in different locations
        local pkl_count=0
        if [ -d "$DATA_TARGET/$dataset/bins" ]; then
            pkl_count=$(ls $DATA_TARGET/$dataset/bins/*.pkl 2>/dev/null | wc -l || echo '0')
            echo "  PKL files (bins/): $pkl_count files"
        else
            pkl_count=$(ls $DATA_TARGET/$dataset/*.pkl 2>/dev/null | wc -l || echo '0')
            echo "  PKL files: $pkl_count files"
        fi

        # Check for key data directories
        data_dirs=("points" "instance_mask" "semantic_mask" "super_points" "super_points_spt")
        found_dirs=""
        for dir in "${data_dirs[@]}"; do
            if [ -d "$DATA_TARGET/$dataset/$dir" ]; then
                count=$(ls "$DATA_TARGET/$dataset/$dir"/*.bin 2>/dev/null | wc -l || echo '0')
                found_dirs="$found_dirs $dir($count)"
            fi
        done
        [ -n "$found_dirs" ] && echo "  Data dirs:$found_dirs"
    else
        echo "✗ $dataset: Directory not found"
    fi
done

echo ""
echo "=== Setup Complete ==="
echo "All datasets have been extracted and organized in: $DATA_TARGET"
echo "You can now proceed with model testing and training."

# UniDet3D Dataset Setup Script
# This script extracts and prepares all six datasets for UniDet3D

set -e  # Exit on any error

echo "=== UniDet3D Dataset Setup ==="
echo "Starting dataset extraction and preparation..."

# Define paths
DATA_SOURCE="/home/<USER>/data/unidet3d"
DATA_TARGET="$(dirname "$0")/../data"  # 相对于scripts目录的上级目录
PROJECT_ROOT="$(dirname "$0")/.."      # 项目根目录

# Create data directory if it doesn't exist
mkdir -p "$DATA_TARGET"

# List of datasets
DATASETS=("scannet" "s3dis" "multiscan" "3rscan" "scannetpp" "arkitscenes")

echo "Extracting datasets..."
for dataset in "${DATASETS[@]}"; do
    echo "Processing $dataset..."
    
    # Check if tar.gz file exists
    if [ -f "$DATA_SOURCE/${dataset}.tar.gz" ]; then
        echo "  - Extracting ${dataset}.tar.gz..."
        tar -xzf "$DATA_SOURCE/${dataset}.tar.gz" -C "$DATA_TARGET"
        echo "  - ${dataset} extracted successfully"
    else
        echo "  - Warning: ${dataset}.tar.gz not found in $DATA_SOURCE"
    fi
done

echo "=== Dataset Extraction Complete ==="
echo "Verifying dataset structure..."

# Verify each dataset directory exists and show basic info
for dataset in "${DATASETS[@]}"; do
    if [ -d "$DATA_TARGET/$dataset" ]; then
        echo "✓ $dataset: $(du -sh $DATA_TARGET/$dataset | cut -f1)"
        
        # Show key files for each dataset
        case $dataset in
            "scannet")
                echo "  Key files: $(ls $DATA_TARGET/$dataset/*.pkl 2>/dev/null | wc -l) pkl files"
                ;;
            "s3dis")
                echo "  Key files: $(ls $DATA_TARGET/$dataset/*.pkl 2>/dev/null | wc -l) pkl files"
                ;;
            "multiscan")
                echo "  Key files: $(ls $DATA_TARGET/$dataset/bins/*.pkl 2>/dev/null | wc -l) pkl files"
                ;;
            "3rscan")
                echo "  Key files: $(ls $DATA_TARGET/$dataset/*.pkl 2>/dev/null | wc -l) pkl files"
                ;;
            "scannetpp")
                echo "  Key files: $(ls $DATA_TARGET/$dataset/bins/*.pkl 2>/dev/null | wc -l) pkl files"
                ;;
            "arkitscenes")
                echo "  Key files: $(ls $DATA_TARGET/$dataset/*.pkl 2>/dev/null | wc -l) pkl files"
                ;;
        esac
    else
        echo "✗ $dataset: Directory not found"
    fi
done

echo ""
echo "=== Setup Complete ==="
echo "All datasets have been extracted to: $DATA_TARGET"
echo "You can now proceed with model testing."
