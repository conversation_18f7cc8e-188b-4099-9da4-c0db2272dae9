#!/bin/bash

# UniDet3D Model Training Script
# This script provides comprehensive training functionality for UniDet3D model

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"  # 获取项目根目录的绝对路径
FULL_CONFIG="configs/unidet3d_1xb8_scannet_s3dis_multiscan_3rscan_scannetpp_arkitscenes.py"
SINGLE_CONFIG="configs/unidet3d_1xb8_scannet.py"
BACKBONE_CHECKPOINT="work_dirs/tmp/oneformer3d_1xb4_scannet.pth"
WORK_DIR_BASE="work_dirs"
DEFAULT_GPU="1"  # 默认使用GPU #1

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to validate GPU configuration
validate_gpu() {
    local gpu_ids="$1"
    print_status "Validating GPU configuration: $gpu_ids"

    # Check if nvidia-smi is available
    if ! command -v nvidia-smi &> /dev/null; then
        print_error "nvidia-smi not found. GPU validation failed."
        exit 1
    fi

    # Get available GPUs
    local available_gpus=$(nvidia-smi --list-gpus | wc -l)
    print_status "Total GPUs available: $available_gpus"

    # Validate each specified GPU
    IFS=',' read -ra GPU_ARRAY <<< "$gpu_ids"
    for gpu_id in "${GPU_ARRAY[@]}"; do
        gpu_id=$(echo "$gpu_id" | xargs)  # trim whitespace
        if [[ ! "$gpu_id" =~ ^[0-9]+$ ]]; then
            print_error "Invalid GPU ID: $gpu_id (must be numeric)"
            exit 1
        fi

        if [ "$gpu_id" -ge "$available_gpus" ]; then
            print_error "GPU #$gpu_id not available (only 0-$((available_gpus-1)) available)"
            exit 1
        fi

        # Check if GPU is accessible
        if ! nvidia-smi -i "$gpu_id" &> /dev/null; then
            print_error "Cannot access GPU #$gpu_id"
            exit 1
        fi

        print_success "GPU #$gpu_id validated"
    done

    # Set CUDA_VISIBLE_DEVICES
    export CUDA_VISIBLE_DEVICES="$gpu_ids"
    print_status "CUDA_VISIBLE_DEVICES set to: $CUDA_VISIBLE_DEVICES"
}

# Function to check prerequisites
check_prerequisites() {
    local gpu_ids="$1"
    print_status "Checking training prerequisites..."

    # Validate GPU configuration
    validate_gpu "$gpu_ids"

    # Check if we're in the correct directory
    if [ ! -f "$FULL_CONFIG" ]; then
        print_error "Configuration file not found: $FULL_CONFIG"
        print_error "Please run this script from the project root: $PROJECT_ROOT"
        exit 1
    fi
    
    # Check if backbone checkpoint exists
    if [ ! -f "$BACKBONE_CHECKPOINT" ]; then
        print_error "Backbone checkpoint not found: $BACKBONE_CHECKPOINT"
        print_error "Please download the backbone checkpoint from:"
        print_error "https://github.com/filapro/oneformer3d/releases/download/v1.0/oneformer3d_1xb4_scannet.pth"
        exit 1
    fi
    
    # Check if datasets are available
    local datasets=("scannet" "s3dis" "multiscan" "3rscan" "scannetpp" "arkitscenes")
    local missing_datasets=()
    
    for dataset in "${datasets[@]}"; do
        if [ ! -d "data/$dataset" ]; then
            missing_datasets+=("$dataset")
        fi
    done
    
    if [ ${#missing_datasets[@]} -gt 0 ]; then
        print_error "Missing datasets: ${missing_datasets[*]}"
        print_error "Please run setup_datasets.sh first to extract datasets"
        exit 1
    fi
    
    # Check GPU availability
    if ! nvidia-smi &> /dev/null; then
        print_warning "nvidia-smi not available. GPU training may not work."
    else
        print_status "GPU Status:"
        nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits
    fi
    
    print_success "Prerequisites check completed"
}

# Function to estimate training time and resources
estimate_training_resources() {
    print_status "Training Resource Estimation:"
    echo "  - Full training (6 datasets, 1024 epochs): ~7-10 days on RTX 4090"
    echo "  - Single dataset training (1024 epochs): ~1-2 days on RTX 4090"
    echo "  - Memory requirement: ~20-24GB GPU memory"
    echo "  - Disk space for checkpoints: ~50-100GB"
    echo "  - Recommended: Monitor GPU temperature and usage"
}

# Function to create training configuration for specific datasets
create_custom_config() {
    local datasets_str="$1"
    local config_name="$2"
    
    IFS=',' read -ra DATASETS <<< "$datasets_str"
    
    print_status "Creating custom configuration for datasets: ${DATASETS[*]}"
    
    python -c "
import sys
sys.path.append('.')
from mmengine.config import Config

# Load full config
cfg = Config.fromfile('$FULL_CONFIG')

# Available datasets and their indices
all_datasets = ['scannet', 's3dis', 'multiscan', '3rscan', 'scannetpp', 'arkitscenes']
selected_datasets = [d.strip() for d in '${datasets_str}'.split(',')]

# Validate dataset names
for dataset in selected_datasets:
    if dataset not in all_datasets:
        print(f'Invalid dataset: {dataset}')
        print(f'Available datasets: {all_datasets}')
        sys.exit(1)

# Get indices of selected datasets
selected_indices = [all_datasets.index(d) for d in selected_datasets]

# Update training dataloader
train_datasets = []
val_datasets = []

# Add selected datasets to training
for idx in selected_indices:
    train_datasets.append(cfg.train_dataloader.dataset.datasets[idx])
    val_datasets.append(cfg.val_dataloader.dataset.datasets[idx])

cfg.train_dataloader.dataset.datasets = train_datasets
cfg.val_dataloader.dataset.datasets = val_datasets
cfg.test_dataloader = cfg.val_dataloader

# Update evaluator
cfg.test_evaluator.datasets = selected_datasets
cfg.test_evaluator.datasets_classes = [cfg.test_evaluator.datasets_classes[all_datasets.index(d)] for d in selected_datasets]
cfg.val_evaluator = cfg.test_evaluator

# Update work directory
cfg.work_dir = f'work_dirs/unidet3d_custom_{\\'_\\'.join(selected_datasets)}'

# Save custom config
cfg.dump('$config_name')
print(f'Custom config saved to: $config_name')
print(f'Work directory: {cfg.work_dir}')
"
}

# Function to start full training
train_full_model() {
    local gpu_ids="$1"
    print_status "Starting full model training on all 6 datasets..."
    print_status "Using GPU(s): $gpu_ids"

    local work_dir="$WORK_DIR_BASE/unidet3d_full_training_$(date +%Y%m%d_%H%M%S)"

    estimate_training_resources

    print_warning "This will start a very long training process (7-10 days)."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Training cancelled."
        return
    fi

    # Determine launcher based on number of GPUs
    local launcher="none"
    local gpu_count=$(echo "$gpu_ids" | tr ',' '\n' | wc -l)
    if [ "$gpu_count" -gt 1 ]; then
        launcher="pytorch"
    fi

    # Start training (GPU controlled by CUDA_VISIBLE_DEVICES)
    python tools/train.py \
        "$FULL_CONFIG" \
        --work-dir "$work_dir" \
        --launcher "$launcher"

    print_success "Full model training completed"
    print_status "Results saved to: $work_dir"
}

# Function to train on single dataset
train_single_dataset() {
    local dataset=$1
    local gpu_ids=$2
    print_status "Training on single dataset: $dataset"
    print_status "Using GPU(s): $gpu_ids"
    
    if [ "$dataset" = "scannet" ]; then
        # Use pre-configured single dataset config
        config_file="$SINGLE_CONFIG"
    else
        # Create custom config for other single datasets
        config_file="configs/temp_${dataset}_train_config.py"
        create_custom_config "$dataset" "$config_file"
    fi
    
    local work_dir="$WORK_DIR_BASE/unidet3d_${dataset}_$(date +%Y%m%d_%H%M%S)"
    
    print_status "Estimated training time: 1-2 days"
    read -p "Continue with training? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Training cancelled."
        return
    fi
    
    # Determine launcher based on number of GPUs
    local launcher="none"
    local gpu_count=$(echo "$gpu_ids" | tr ',' '\n' | wc -l)
    if [ "$gpu_count" -gt 1 ]; then
        launcher="pytorch"
    fi

    # Start training (GPU controlled by CUDA_VISIBLE_DEVICES)
    python tools/train.py \
        "$config_file" \
        --work-dir "$work_dir" \
        --launcher "$launcher"
    
    # Clean up temporary config if created
    if [ "$config_file" != "$SINGLE_CONFIG" ]; then
        rm -f "$config_file"
    fi
    
    print_success "Single dataset training completed for: $dataset"
    print_status "Results saved to: $work_dir"
}

# Function to train on custom dataset combination
train_custom_datasets() {
    local datasets="$1"
    local gpu_ids="$2"
    print_status "Training on custom dataset combination: $datasets"
    print_status "Using GPU(s): $gpu_ids"
    
    local config_file="configs/temp_custom_train_config.py"
    create_custom_config "$datasets" "$config_file"
    
    local work_dir="$WORK_DIR_BASE/unidet3d_custom_$(date +%Y%m%d_%H%M%S)"
    
    print_status "Starting custom training..."
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Training cancelled."
        rm -f "$config_file"
        return
    fi
    
    # Determine launcher based on number of GPUs
    local launcher="none"
    local gpu_count=$(echo "$gpu_ids" | tr ',' '\n' | wc -l)
    if [ "$gpu_count" -gt 1 ]; then
        launcher="pytorch"
    fi

    # Start training (GPU controlled by CUDA_VISIBLE_DEVICES)
    python tools/train.py \
        "$config_file" \
        --work-dir "$work_dir" \
        --launcher "$launcher"
    
    # Clean up temporary config
    rm -f "$config_file"
    
    print_success "Custom training completed"
    print_status "Results saved to: $work_dir"
}

# Function to resume training
resume_training() {
    local work_dir="$1"
    local checkpoint="${2:-auto}"
    
    if [ ! -d "$work_dir" ]; then
        print_error "Work directory not found: $work_dir"
        exit 1
    fi
    
    print_status "Resuming training from: $work_dir"
    
    # Find config file in work directory
    local config_file=$(find "$work_dir" -name "*.py" | head -1)
    if [ -z "$config_file" ]; then
        print_error "No config file found in work directory"
        exit 1
    fi
    
    python tools/train.py \
        "$config_file" \
        --work-dir "$work_dir" \
        --resume "$checkpoint" \
        --launcher none
    
    print_success "Training resumed"
}

# Function to monitor training progress
monitor_training() {
    local work_dir="$1"

    if [ ! -d "$work_dir" ]; then
        print_error "Work directory not found: $work_dir"
        exit 1
    fi

    print_status "Monitoring training progress in: $work_dir"

    # Show latest log entries
    local log_file=$(find "$work_dir" -name "*.log" | head -1)
    if [ -n "$log_file" ]; then
        echo "Latest log entries:"
        tail -20 "$log_file"

        # Extract training metrics
        echo ""
        echo "Recent training metrics:"
        grep -E "(loss|mAP|epoch)" "$log_file" | tail -10
    fi

    # Show checkpoint files
    echo ""
    echo "Available checkpoints:"
    ls -la "$work_dir"/*.pth 2>/dev/null || echo "No checkpoints found yet"

    # Show GPU usage
    echo ""
    echo "Current GPU usage:"
    nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits

    # Show disk usage
    echo ""
    echo "Disk usage for work directory:"
    du -sh "$work_dir"
}

# Function to display usage
show_usage() {
    echo "Usage: $0 [OPTION] [ARGS] [--gpu GPU_IDS]"
    echo ""
    echo "Options:"
    echo "  full                    Train on all 6 datasets"
    echo "  single DATASET         Train on single dataset"
    echo "                         Available: scannet, s3dis, multiscan, 3rscan, scannetpp, arkitscenes"
    echo "  custom DATASETS        Train on custom dataset combination (comma-separated)"
    echo "                         Example: scannet,s3dis,multiscan"
    echo "  resume WORK_DIR [CKPT] Resume training from work directory"
    echo "  monitor WORK_DIR       Monitor training progress"
    echo "  check                  Check prerequisites only"
    echo "  help                   Show this help message"
    echo ""
    echo "GPU Configuration:"
    echo "  --gpu GPU_IDS          Specify GPU IDs to use (default: 1)"
    echo "                         Single GPU: --gpu 1"
    echo "                         Multiple GPUs: --gpu 1,2"
    echo ""
    echo "Examples:"
    echo "  $0 full --gpu 1                           # Train on all datasets using GPU #1"
    echo "  $0 single scannet --gpu 1                 # Train on ScanNet using GPU #1"
    echo "  $0 custom scannet,s3dis --gpu 1           # Train on ScanNet and S3DIS using GPU #1"
    echo "  $0 resume work_dirs/training_dir --gpu 1  # Resume training using GPU #1"
    echo "  $0 monitor work_dirs/training_dir         # Monitor progress (no GPU needed)"
    echo "  $0 check --gpu 1                         # Check prerequisites for GPU #1"
}

# Function to parse arguments
parse_train_arguments() {
    local gpu_ids="$DEFAULT_GPU"
    local action=""
    local dataset=""
    local work_dir=""
    local checkpoint=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --gpu)
                gpu_ids="$2"
                shift 2
                ;;
            full|single|custom|resume|monitor|check|help|-h|--help)
                action="$1"
                shift
                ;;
            scannet|s3dis|multiscan|3rscan|scannetpp|arkitscenes)
                if [ "$action" = "single" ]; then
                    dataset="$1"
                elif [ "$action" = "custom" ]; then
                    dataset="$1"
                fi
                shift
                ;;
            work_dirs/*)
                work_dir="$1"
                shift
                ;;
            *.pth)
                checkpoint="$1"
                shift
                ;;
            *,*)
                # Custom dataset combination
                if [ "$action" = "custom" ]; then
                    dataset="$1"
                fi
                shift
                ;;
            *)
                if [ -z "$action" ]; then
                    action="$1"
                elif [ -z "$dataset" ] && [[ "$action" =~ ^(single|custom)$ ]]; then
                    dataset="$1"
                elif [ -z "$work_dir" ] && [[ "$action" =~ ^(resume|monitor)$ ]]; then
                    work_dir="$1"
                elif [ -z "$checkpoint" ] && [ "$action" = "resume" ]; then
                    checkpoint="$1"
                else
                    print_error "Unknown argument: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # Set default action if none specified
    [ -z "$action" ] && action="help"

    echo "$action|$dataset|$work_dir|$checkpoint|$gpu_ids"
}

# Main execution
main() {
    echo "=== UniDet3D Model Training ==="
    echo "Project: $PROJECT_ROOT"
    echo "Full Config: $FULL_CONFIG"
    echo "Backbone: $BACKBONE_CHECKPOINT"
    echo ""

    # Parse arguments
    local parsed=$(parse_train_arguments "$@")
    IFS='|' read -r action dataset work_dir checkpoint gpu_ids <<< "$parsed"

    echo "Action: $action"
    echo "Dataset: ${dataset:-all}"
    echo "Work Dir: ${work_dir:-auto}"
    echo "Checkpoint: ${checkpoint:-auto}"
    echo "GPU(s): $gpu_ids"
    echo ""

    # Change to project directory
    cd "$PROJECT_ROOT"

    # Set Python path for UniDet3D module
    export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
    export OMP_NUM_THREADS=16

    case "$action" in
        "full")
            check_prerequisites "$gpu_ids"
            train_full_model "$gpu_ids"
            ;;
        "single")
            if [ -z "$dataset" ]; then
                print_error "Dataset name required for single dataset training"
                show_usage
                exit 1
            fi
            check_prerequisites "$gpu_ids"
            train_single_dataset "$dataset" "$gpu_ids"
            ;;
        "custom")
            if [ -z "$dataset" ]; then
                print_error "Dataset list required for custom training"
                show_usage
                exit 1
            fi
            check_prerequisites "$gpu_ids"
            train_custom_datasets "$dataset" "$gpu_ids"
            ;;
        "resume")
            if [ -z "$work_dir" ]; then
                print_error "Work directory required for resume"
                show_usage
                exit 1
            fi
            resume_training "$work_dir" "$checkpoint"
            ;;
        "monitor")
            if [ -z "$work_dir" ]; then
                print_error "Work directory required for monitoring"
                show_usage
                exit 1
            fi
            monitor_training "$work_dir"
            ;;
        "check")
            check_prerequisites "$gpu_ids"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
