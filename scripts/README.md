# UniDet3D Scripts Directory

This directory contains all the utility scripts for UniDet3D project operations.

## Scripts Overview

### 📦 Data Management
- **`setup_datasets.sh`** - Automated dataset extraction and setup
  - Extracts all 6 datasets from tar.gz files
  - Verifies data structure and integrity
  - Creates proper directory structure

### 🧪 Model Testing
- **`test_unidet3d.sh`** - Comprehensive model testing script
  - Full testing on all 6 datasets
  - Single dataset testing
  - Visualization generation
  - Environment validation

- **`interpret_results.py`** - Test results analysis tool
  - Performance comparison with paper results
  - Detailed metrics analysis
  - Report generation

### 🏋️ Model Training
- **`train_unidet3d.sh`** - Complete training management script
  - Full multi-dataset training
  - Single dataset training
  - Custom dataset combination training
  - Training monitoring and resumption

- **`analyze_training.py`** - Training progress analysis tool
  - Training curve visualization
  - Validation metrics plotting
  - Comprehensive training reports

### 🔄 Data Preprocessing
- **`data_preprocessing_guide.py`** - Data format conversion tool
  - Comprehensive data format guide
  - Custom data conversion utilities
  - Support for PLY, PCD, TXT formats

## Usage Examples

### Quick Start
```bash
# Make scripts executable
chmod +x scripts/*.sh scripts/*.py

# Setup datasets
./scripts/setup_datasets.sh

# Test model
./scripts/test_unidet3d.sh full

# Analyze results
python scripts/interpret_results.py work_dirs/test_results
```

### Training
```bash
# Check environment
./scripts/train_unidet3d.sh check

# Start training
./scripts/train_unidet3d.sh full

# Monitor training
./scripts/train_unidet3d.sh monitor work_dirs/training_dir
```

### Data Preprocessing
```bash
# View data format guide
python scripts/data_preprocessing_guide.py --guide

# Convert custom data
python scripts/data_preprocessing_guide.py --convert /path/to/data --output data/custom
```

## Script Dependencies

All scripts are designed to work from the project root directory and automatically handle path resolution. They include:

- Automatic project root detection
- Environment validation
- Error handling and user feedback
- Progress monitoring
- Comprehensive logging

## Notes

- All scripts should be run from the project root directory (`/home/<USER>/repos/unidet3d`)
- Scripts automatically detect and use relative paths
- Docker environment compatibility ensured
- RTX 4090 GPU optimization included
