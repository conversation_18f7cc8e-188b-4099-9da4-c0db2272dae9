#!/usr/bin/env python3
"""
UniDet3D Results Interpretation Tool
This script helps interpret and analyze UniDet3D testing results
"""

import json
import os
import re
import argparse
from pathlib import Path
from typing import Dict, List, Any

class UniDet3DResultsAnalyzer:
    """Analyzer for UniDet3D testing results"""
    
    def __init__(self, work_dir: str):
        self.work_dir = Path(work_dir)
        self.expected_metrics = {
            'scannet': {'mAP_25': 77.0, 'mAP_50': 65.9},
            'arkitscenes': {'mAP_25': 60.1, 'mAP_50': 47.2},
            's3dis': {'mAP_25': 76.7, 'mAP_50': 65.3},
            'multiscan': {'mAP_25': 62.6, 'mAP_50': 52.3},
            '3rscan': {'mAP_25': 63.6, 'mAP_50': 44.9},
            'scannetpp': {'mAP_25': 24.0, 'mAP_50': 16.8}
        }
        
        self.class_names = {
            'scannet': ['cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window', 'bookshelf',
                       'picture', 'counter', 'desk', 'curtain', 'refrigerator', 'showercurtrain',
                       'toilet', 'sink', 'bathtub', 'otherfurniture'],
            's3dis': ['table', 'chair', 'sofa', 'bookcase', 'board'],
            'multiscan': ['door', 'table', 'chair', 'cabinet', 'window', 'sofa', 'microwave', 'pillow',
                         'tv_monitor', 'curtain', 'trash_can', 'suitcase', 'sink', 'backpack', 'bed',
                         'refrigerator', 'toilet'],
            '3rscan': ['cabinet', 'bed', 'chair', 'sofa', 'table', 'door', 'window', 'bookshelf',
                      'picture', 'counter', 'desk', 'curtain', 'refrigerator', 'showercurtrain',
                      'toilet', 'sink', 'bathtub', 'otherfurniture'],
            'arkitscenes': ['cabinet', 'refrigerator', 'shelf', 'stove', 'bed', 'sink', 'washer',
                           'toilet', 'bathtub', 'oven', 'dishwasher', 'fireplace', 'stool', 'chair',
                           'table', 'tv_monitor', 'sofa']
        }
    
    def find_log_files(self) -> List[Path]:
        """Find all log files in the work directory"""
        log_files = []
        for log_file in self.work_dir.rglob("*.log"):
            log_files.append(log_file)
        return log_files
    
    def parse_log_file(self, log_file: Path) -> Dict[str, Any]:
        """Parse a log file to extract metrics"""
        results = {}
        
        try:
            with open(log_file, 'r') as f:
                content = f.read()
            
            # Extract mAP metrics for each dataset
            for dataset in self.expected_metrics.keys():
                dataset_pattern = rf"{dataset}.*?mAP_0\.25:\s*([\d.]+).*?mAP_0\.50:\s*([\d.]+)"
                match = re.search(dataset_pattern, content, re.DOTALL | re.IGNORECASE)
                
                if match:
                    results[dataset] = {
                        'mAP_25': float(match.group(1)),
                        'mAP_50': float(match.group(2))
                    }
            
            # Extract per-class metrics if available
            self._extract_per_class_metrics(content, results)
            
        except Exception as e:
            print(f"Error parsing log file {log_file}: {e}")
        
        return results
    
    def _extract_per_class_metrics(self, content: str, results: Dict[str, Any]):
        """Extract per-class AP metrics from log content"""
        # This is a simplified extraction - actual implementation would depend on log format
        pass
    
    def compare_with_expected(self, results: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """Compare results with expected performance"""
        comparison = {}
        
        for dataset, metrics in results.items():
            if dataset in self.expected_metrics:
                expected = self.expected_metrics[dataset]
                comparison[dataset] = {}
                
                for metric, value in metrics.items():
                    if metric in expected:
                        expected_value = expected[metric]
                        diff = value - expected_value
                        comparison[dataset][metric] = {
                            'actual': value,
                            'expected': expected_value,
                            'difference': diff,
                            'percentage_diff': (diff / expected_value) * 100 if expected_value > 0 else 0
                        }
        
        return comparison
    
    def generate_report(self, results: Dict[str, Any], comparison: Dict[str, Dict[str, float]]) -> str:
        """Generate a comprehensive analysis report"""
        report = []
        report.append("=" * 80)
        report.append("UniDet3D Testing Results Analysis")
        report.append("=" * 80)
        report.append("")
        
        # Overall summary
        report.append("OVERALL SUMMARY")
        report.append("-" * 40)
        
        total_datasets = len(self.expected_metrics)
        tested_datasets = len(results)
        report.append(f"Datasets tested: {tested_datasets}/{total_datasets}")
        
        if tested_datasets == 0:
            report.append("No results found. Please check if testing completed successfully.")
            return "\n".join(report)
        
        report.append("")
        
        # Per-dataset analysis
        report.append("PER-DATASET ANALYSIS")
        report.append("-" * 40)
        
        for dataset in sorted(results.keys()):
            report.append(f"\n{dataset.upper()}:")
            
            if dataset in comparison:
                comp = comparison[dataset]
                for metric in ['mAP_25', 'mAP_50']:
                    if metric in comp:
                        data = comp[metric]
                        status = "✓" if abs(data['percentage_diff']) < 5 else "⚠" if abs(data['percentage_diff']) < 10 else "✗"
                        report.append(f"  {metric}: {data['actual']:.1f} (expected: {data['expected']:.1f}) "
                                    f"[{data['difference']:+.1f}, {data['percentage_diff']:+.1f}%] {status}")
            else:
                # Just show actual results if no expected values
                metrics = results[dataset]
                for metric, value in metrics.items():
                    report.append(f"  {metric}: {value:.1f}")
        
        report.append("")
        
        # Performance assessment
        report.append("PERFORMANCE ASSESSMENT")
        report.append("-" * 40)
        
        good_performance = []
        acceptable_performance = []
        poor_performance = []
        
        for dataset, comp in comparison.items():
            avg_diff = sum(data['percentage_diff'] for data in comp.values()) / len(comp)
            if abs(avg_diff) < 5:
                good_performance.append(dataset)
            elif abs(avg_diff) < 10:
                acceptable_performance.append(dataset)
            else:
                poor_performance.append(dataset)
        
        if good_performance:
            report.append(f"✓ Good performance (±5%): {', '.join(good_performance)}")
        if acceptable_performance:
            report.append(f"⚠ Acceptable performance (±10%): {', '.join(acceptable_performance)}")
        if poor_performance:
            report.append(f"✗ Poor performance (>10% diff): {', '.join(poor_performance)}")
        
        report.append("")
        
        # Recommendations
        report.append("RECOMMENDATIONS")
        report.append("-" * 40)
        
        if poor_performance:
            report.append("• Consider re-running tests for datasets with poor performance")
            report.append("• Check if datasets were properly extracted and preprocessed")
            report.append("• Verify that the correct pre-trained checkpoint is being used")
        
        if not good_performance and not acceptable_performance:
            report.append("• All datasets show significant performance deviation")
            report.append("• Check environment setup and dependencies")
            report.append("• Verify GPU memory and computational resources")
        
        if len(results) < total_datasets:
            missing = set(self.expected_metrics.keys()) - set(results.keys())
            report.append(f"• Missing results for: {', '.join(missing)}")
            report.append("• Consider running full dataset testing")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def analyze(self) -> str:
        """Main analysis function"""
        print(f"Analyzing results in: {self.work_dir}")
        
        log_files = self.find_log_files()
        if not log_files:
            return "No log files found in the work directory."
        
        print(f"Found {len(log_files)} log file(s)")
        
        all_results = {}
        for log_file in log_files:
            print(f"Parsing: {log_file}")
            results = self.parse_log_file(log_file)
            all_results.update(results)
        
        if not all_results:
            return "No metrics found in log files. Please check if testing completed successfully."
        
        comparison = self.compare_with_expected(all_results)
        report = self.generate_report(all_results, comparison)
        
        return report

def main():
    parser = argparse.ArgumentParser(description="Analyze UniDet3D testing results")
    parser.add_argument("work_dir", help="Work directory containing test results")
    parser.add_argument("--output", "-o", help="Output file for the report")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.work_dir):
        print(f"Error: Work directory not found: {args.work_dir}")
        return
    
    analyzer = UniDet3DResultsAnalyzer(args.work_dir)
    report = analyzer.analyze()
    
    print(report)
    
    if args.output:
        with open(args.output, 'w') as f:
            f.write(report)
        print(f"\nReport saved to: {args.output}")

if __name__ == "__main__":
    main()
