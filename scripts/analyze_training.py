#!/usr/bin/env python3
"""
UniDet3D Training Analysis Tool
This script analyzes training logs and provides insights into training progress
"""

import re
import json
import argparse
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Any
from datetime import datetime

class TrainingAnalyzer:
    """Analyzer for UniDet3D training logs and checkpoints"""
    
    def __init__(self, work_dir: str):
        self.work_dir = Path(work_dir)
        self.log_file = self._find_log_file()
        self.metrics_history = {}
        
    def _find_log_file(self) -> Path:
        """Find the training log file"""
        log_files = list(self.work_dir.glob("*.log"))
        if not log_files:
            raise FileNotFoundError(f"No log file found in {self.work_dir}")
        return log_files[0]  # Use the first log file found
    
    def parse_training_log(self) -> Dict[str, List[float]]:
        """Parse training log to extract metrics"""
        metrics = {
            'epoch': [],
            'loss': [],
            'lr': [],
            'time': [],
            'data_time': [],
            'memory': []
        }
        
        # Add dataset-specific mAP metrics
        datasets = ['scannet', 's3dis', 'multiscan', '3rscan', 'scannetpp', 'arkitscenes']
        for dataset in datasets:
            metrics[f'{dataset}_mAP_25'] = []
            metrics[f'{dataset}_mAP_50'] = []
        
        try:
            with open(self.log_file, 'r') as f:
                content = f.read()
            
            # Parse training iterations
            iter_pattern = r'Epoch\s*\[(\d+)\]\s*\[(\d+)/\d+\].*?loss:\s*([\d.]+).*?lr:\s*([\d.e-]+).*?time:\s*([\d.]+).*?data_time:\s*([\d.]+).*?memory:\s*(\d+)'
            
            for match in re.finditer(iter_pattern, content):
                epoch = int(match.group(1))
                loss = float(match.group(3))
                lr = float(match.group(4))
                time = float(match.group(5))
                data_time = float(match.group(6))
                memory = int(match.group(7))
                
                metrics['epoch'].append(epoch)
                metrics['loss'].append(loss)
                metrics['lr'].append(lr)
                metrics['time'].append(time)
                metrics['data_time'].append(data_time)
                metrics['memory'].append(memory)
            
            # Parse validation metrics
            for dataset in datasets:
                val_pattern = rf'{dataset}.*?mAP_0\.25:\s*([\d.]+).*?mAP_0\.50:\s*([\d.]+)'
                for match in re.finditer(val_pattern, content, re.IGNORECASE):
                    metrics[f'{dataset}_mAP_25'].append(float(match.group(1)))
                    metrics[f'{dataset}_mAP_50'].append(float(match.group(2)))
            
        except Exception as e:
            print(f"Error parsing log file: {e}")
        
        self.metrics_history = metrics
        return metrics
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get summary of training progress"""
        if not self.metrics_history:
            self.parse_training_log()
        
        metrics = self.metrics_history
        summary = {}
        
        if metrics['epoch']:
            summary['total_epochs'] = max(metrics['epoch'])
            summary['total_iterations'] = len(metrics['epoch'])
            summary['current_loss'] = metrics['loss'][-1] if metrics['loss'] else 0
            summary['best_loss'] = min(metrics['loss']) if metrics['loss'] else 0
            summary['current_lr'] = metrics['lr'][-1] if metrics['lr'] else 0
            summary['avg_time_per_iter'] = np.mean(metrics['time']) if metrics['time'] else 0
            summary['avg_memory_usage'] = np.mean(metrics['memory']) if metrics['memory'] else 0
        
        # Get best mAP scores for each dataset
        datasets = ['scannet', 's3dis', 'multiscan', '3rscan', 'scannetpp', 'arkitscenes']
        summary['best_mAP'] = {}
        
        for dataset in datasets:
            mAP_25_key = f'{dataset}_mAP_25'
            mAP_50_key = f'{dataset}_mAP_50'
            
            if metrics.get(mAP_25_key):
                summary['best_mAP'][dataset] = {
                    'mAP_25': max(metrics[mAP_25_key]),
                    'mAP_50': max(metrics[mAP_50_key]) if metrics.get(mAP_50_key) else 0
                }
        
        return summary
    
    def plot_training_curves(self, save_path: str = None):
        """Plot training curves"""
        if not self.metrics_history:
            self.parse_training_log()
        
        metrics = self.metrics_history
        
        if not metrics['epoch']:
            print("No training data found to plot")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('UniDet3D Training Analysis', fontsize=16)
        
        # Plot loss curve
        if metrics['loss']:
            axes[0, 0].plot(metrics['loss'])
            axes[0, 0].set_title('Training Loss')
            axes[0, 0].set_xlabel('Iteration')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].grid(True)
        
        # Plot learning rate
        if metrics['lr']:
            axes[0, 1].plot(metrics['lr'])
            axes[0, 1].set_title('Learning Rate')
            axes[0, 1].set_xlabel('Iteration')
            axes[0, 1].set_ylabel('LR')
            axes[0, 1].set_yscale('log')
            axes[0, 1].grid(True)
        
        # Plot memory usage
        if metrics['memory']:
            axes[1, 0].plot(metrics['memory'])
            axes[1, 0].set_title('GPU Memory Usage (MB)')
            axes[1, 0].set_xlabel('Iteration')
            axes[1, 0].set_ylabel('Memory (MB)')
            axes[1, 0].grid(True)
        
        # Plot time per iteration
        if metrics['time']:
            axes[1, 1].plot(metrics['time'])
            axes[1, 1].set_title('Time per Iteration')
            axes[1, 1].set_xlabel('Iteration')
            axes[1, 1].set_ylabel('Time (s)')
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Training curves saved to: {save_path}")
        else:
            plt.show()
    
    def plot_validation_metrics(self, save_path: str = None):
        """Plot validation mAP metrics for all datasets"""
        if not self.metrics_history:
            self.parse_training_log()
        
        metrics = self.metrics_history
        datasets = ['scannet', 's3dis', 'multiscan', '3rscan', 'scannetpp', 'arkitscenes']
        
        # Check if we have validation data
        has_val_data = any(metrics.get(f'{d}_mAP_25') for d in datasets)
        if not has_val_data:
            print("No validation data found to plot")
            return
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        fig.suptitle('Validation mAP Scores by Dataset', fontsize=16)
        axes = axes.flatten()
        
        for i, dataset in enumerate(datasets):
            mAP_25_key = f'{dataset}_mAP_25'
            mAP_50_key = f'{dataset}_mAP_50'
            
            if metrics.get(mAP_25_key):
                axes[i].plot(metrics[mAP_25_key], label='mAP@0.25', marker='o')
                if metrics.get(mAP_50_key):
                    axes[i].plot(metrics[mAP_50_key], label='mAP@0.50', marker='s')
                
                axes[i].set_title(f'{dataset.upper()}')
                axes[i].set_xlabel('Validation Epoch')
                axes[i].set_ylabel('mAP')
                axes[i].legend()
                axes[i].grid(True)
                axes[i].set_ylim(0, 100)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Validation curves saved to: {save_path}")
        else:
            plt.show()
    
    def generate_report(self) -> str:
        """Generate a comprehensive training report"""
        summary = self.get_training_summary()
        
        report = []
        report.append("=" * 80)
        report.append("UniDet3D Training Analysis Report")
        report.append("=" * 80)
        report.append(f"Work Directory: {self.work_dir}")
        report.append(f"Log File: {self.log_file}")
        report.append(f"Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Training Progress
        report.append("TRAINING PROGRESS")
        report.append("-" * 40)
        if summary.get('total_epochs'):
            report.append(f"Total Epochs: {summary['total_epochs']}")
            report.append(f"Total Iterations: {summary['total_iterations']}")
            report.append(f"Current Loss: {summary['current_loss']:.4f}")
            report.append(f"Best Loss: {summary['best_loss']:.4f}")
            report.append(f"Current Learning Rate: {summary['current_lr']:.2e}")
            report.append(f"Average Time per Iteration: {summary['avg_time_per_iter']:.2f}s")
            report.append(f"Average GPU Memory Usage: {summary['avg_memory_usage']:.0f}MB")
        else:
            report.append("No training data found")
        
        report.append("")
        
        # Validation Results
        report.append("VALIDATION RESULTS")
        report.append("-" * 40)
        if summary.get('best_mAP'):
            for dataset, scores in summary['best_mAP'].items():
                report.append(f"{dataset.upper()}:")
                report.append(f"  Best mAP@0.25: {scores['mAP_25']:.1f}")
                report.append(f"  Best mAP@0.50: {scores['mAP_50']:.1f}")
        else:
            report.append("No validation data found")
        
        report.append("")
        
        # Checkpoints
        report.append("CHECKPOINTS")
        report.append("-" * 40)
        checkpoint_files = list(self.work_dir.glob("*.pth"))
        if checkpoint_files:
            for ckpt in sorted(checkpoint_files):
                size_mb = ckpt.stat().st_size / (1024 * 1024)
                report.append(f"{ckpt.name}: {size_mb:.1f}MB")
        else:
            report.append("No checkpoint files found")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description="Analyze UniDet3D training progress")
    parser.add_argument("work_dir", help="Training work directory")
    parser.add_argument("--plot-training", action="store_true", help="Plot training curves")
    parser.add_argument("--plot-validation", action="store_true", help="Plot validation metrics")
    parser.add_argument("--save-plots", help="Directory to save plots")
    parser.add_argument("--output", "-o", help="Output file for the report")
    parser.add_argument("--gpu", default="1", help="GPU ID to use for analysis (default: 1)")

    args = parser.parse_args()

    # Set GPU environment
    import os
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
    print(f"Using GPU: {args.gpu}")
    
    if not Path(args.work_dir).exists():
        print(f"Error: Work directory not found: {args.work_dir}")
        return
    
    analyzer = TrainingAnalyzer(args.work_dir)
    
    # Generate report
    report = analyzer.generate_report()
    print(report)
    
    if args.output:
        with open(args.output, 'w') as f:
            f.write(report)
        print(f"\nReport saved to: {args.output}")
    
    # Generate plots
    if args.plot_training:
        save_path = None
        if args.save_plots:
            Path(args.save_plots).mkdir(parents=True, exist_ok=True)
            save_path = Path(args.save_plots) / "training_curves.png"
        analyzer.plot_training_curves(save_path)
    
    if args.plot_validation:
        save_path = None
        if args.save_plots:
            Path(args.save_plots).mkdir(parents=True, exist_ok=True)
            save_path = Path(args.save_plots) / "validation_curves.png"
        analyzer.plot_validation_metrics(save_path)

if __name__ == "__main__":
    main()
