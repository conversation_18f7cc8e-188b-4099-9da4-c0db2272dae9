#!/usr/bin/env python3
"""
UniDet3D Data Preprocessing Guide and Converter
This script provides comprehensive guidance and tools for converting custom point cloud data
to UniDet3D format
"""

import os
import numpy as np
import argparse
import pickle
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
import json

try:
    import open3d as o3d
    HAS_OPEN3D = True
except ImportError:
    HAS_OPEN3D = False
    print("Warning: open3d not available. PLY/PCD support limited.")

try:
    from plyfile import PlyData, PlyElement
    HAS_PLYFILE = True
except ImportError:
    HAS_PLYFILE = False
    print("Warning: plyfile not available. PLY support limited.")

def load_ply_file(ply_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load point cloud from PLY file"""
    if HAS_OPEN3D:
        pcd = o3d.io.read_point_cloud(str(ply_path))
        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors) * 255  # Convert to 0-255 range
        return points, colors
    elif HAS_PLYFILE:
        plydata = PlyData.read(ply_path)
        vertex = plydata['vertex']
        points = np.column_stack([vertex['x'], vertex['y'], vertex['z']])
        if 'red' in vertex.dtype.names:
            colors = np.column_stack([vertex['red'], vertex['green'], vertex['blue']])
        else:
            colors = np.ones((len(points), 3)) * 128  # Default gray
        return points, colors
    else:
        raise ImportError("Neither open3d nor plyfile available for PLY loading")

def load_pcd_file(pcd_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load point cloud from PCD file"""
    if not HAS_OPEN3D:
        raise ImportError("open3d required for PCD loading")

    pcd = o3d.io.read_point_cloud(str(pcd_path))
    points = np.asarray(pcd.points)
    colors = np.asarray(pcd.colors) * 255  # Convert to 0-255 range
    return points, colors

def load_txt_file(txt_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load point cloud from TXT file (assumes XYZRGB format)"""
    data = np.loadtxt(txt_path)
    if data.shape[1] >= 6:
        points = data[:, :3]
        colors = data[:, 3:6]
    elif data.shape[1] == 3:
        points = data
        colors = np.ones((len(points), 3)) * 128  # Default gray
    else:
        raise ValueError(f"Unexpected data format in {txt_path}")
    return points, colors

class UniDet3DDataConverter:
    """Converter for custom point cloud data to UniDet3D format"""
    
    def __init__(self, output_dir: str):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create required subdirectories
        self.points_dir = self.output_dir / "points"
        self.instance_mask_dir = self.output_dir / "instance_mask"
        self.semantic_mask_dir = self.output_dir / "semantic_mask"
        self.super_points_dir = self.output_dir / "super_points"
        self.bboxs_dir = self.output_dir / "bboxs"
        
        for dir_path in [self.points_dir, self.instance_mask_dir, 
                        self.semantic_mask_dir, self.super_points_dir, self.bboxs_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def convert_point_cloud(self, 
                           points: np.ndarray,
                           colors: np.ndarray,
                           semantic_labels: np.ndarray,
                           instance_labels: np.ndarray,
                           bboxes_3d: np.ndarray,
                           bbox_labels: np.ndarray,
                           scene_id: str,
                           superpoints: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Convert a single point cloud scene to UniDet3D format
        
        Args:
            points: (N, 3) array of XYZ coordinates
            colors: (N, 3) array of RGB colors (0-255)
            semantic_labels: (N,) array of semantic class labels
            instance_labels: (N,) array of instance IDs
            bboxes_3d: (M, 7) array of 3D bounding boxes [x, y, z, w, l, h, yaw]
            bbox_labels: (M,) array of bbox class labels
            scene_id: unique identifier for the scene
            superpoints: (N,) array of superpoint IDs (optional)
            
        Returns:
            Dictionary containing scene information for pkl file
        """
        
        # Validate input dimensions
        assert points.shape[1] == 3, "Points must be (N, 3)"
        assert colors.shape[1] == 3, "Colors must be (N, 3)"
        assert len(points) == len(colors) == len(semantic_labels) == len(instance_labels)
        
        # Combine points and colors (XYZRGB format)
        point_cloud = np.concatenate([points, colors], axis=1).astype(np.float32)
        
        # Save point cloud as binary file
        points_file = self.points_dir / f"{scene_id}.bin"
        point_cloud.tofile(points_file)
        
        # Save semantic mask
        semantic_file = self.semantic_mask_dir / f"{scene_id}.bin"
        semantic_labels.astype(np.int64).tofile(semantic_file)
        
        # Save instance mask
        instance_file = self.instance_mask_dir / f"{scene_id}.bin"
        instance_labels.astype(np.int64).tofile(instance_file)
        
        # Save bounding boxes
        bbox_file = self.bboxs_dir / f"{scene_id}.npy"
        np.save(bbox_file, bboxes_3d.astype(np.float32))
        
        # Generate or save superpoints
        if superpoints is None:
            # Generate simple superpoints (each point is its own superpoint)
            superpoints = np.arange(len(points), dtype=np.int64)
        
        superpoints_file = self.super_points_dir / f"{scene_id}.bin"
        superpoints.astype(np.int64).tofile(superpoints_file)
        
        # Create scene info dictionary
        scene_info = {
            'lidar_points': {
                'lidar_path': f"points/{scene_id}.bin",
                'num_pts_feats': 6  # XYZRGB
            },
            'pts_instance_mask_path': f"instance_mask/{scene_id}.bin",
            'pts_semantic_mask_path': f"semantic_mask/{scene_id}.bin",
            'super_pts_path': f"super_points/{scene_id}.bin",
            'annos': {
                'gt_num': len(bboxes_3d),
                'name': bbox_labels,
                'location': bboxes_3d[:, :3],  # xyz center
                'dimensions': bboxes_3d[:, 3:6],  # wlh
                'rotation_y': bboxes_3d[:, 6],  # yaw angle
                'gt_boxes_upright_depth': bboxes_3d,
                'class': bbox_labels,
                'difficulty': np.zeros(len(bboxes_3d), dtype=np.int32)
            }
        }
        
        return scene_info
    
    def create_info_files(self, 
                         scene_infos: List[Dict[str, Any]], 
                         train_ratio: float = 0.7,
                         val_ratio: float = 0.2) -> None:
        """
        Create train/val/test split info files
        
        Args:
            scene_infos: List of scene information dictionaries
            train_ratio: Ratio of scenes for training
            val_ratio: Ratio of scenes for validation (rest goes to test)
        """
        
        # Shuffle and split scenes
        np.random.shuffle(scene_infos)
        n_scenes = len(scene_infos)
        n_train = int(n_scenes * train_ratio)
        n_val = int(n_scenes * val_ratio)
        
        train_infos = scene_infos[:n_train]
        val_infos = scene_infos[n_train:n_train + n_val]
        test_infos = scene_infos[n_train + n_val:]
        
        # Save info files
        train_file = self.output_dir / "custom_infos_train.pkl"
        val_file = self.output_dir / "custom_infos_val.pkl"
        test_file = self.output_dir / "custom_infos_test.pkl"
        
        with open(train_file, 'wb') as f:
            pickle.dump(train_infos, f)
        
        with open(val_file, 'wb') as f:
            pickle.dump(val_infos, f)
        
        with open(test_file, 'wb') as f:
            pickle.dump(test_infos, f)
        
        print(f"Created info files:")
        print(f"  Train: {len(train_infos)} scenes -> {train_file}")
        print(f"  Val: {len(val_infos)} scenes -> {val_file}")
        print(f"  Test: {len(test_infos)} scenes -> {test_file}")

def print_data_format_guide():
    """Print comprehensive data format guide"""
    
    guide = """
=== UniDet3D Data Format Guide ===

UniDet3D expects point cloud data in a specific format. This guide explains the required
data structure and provides tools to convert your custom data.

REQUIRED DATA STRUCTURE:
├── points/                 # Point cloud files (.bin)
│   ├── scene_001.bin      # XYZRGB format (N×6 float32)
│   └── scene_002.bin
├── instance_mask/          # Instance segmentation masks (.bin)
│   ├── scene_001.bin      # Instance IDs (N×1 int64)
│   └── scene_002.bin
├── semantic_mask/          # Semantic segmentation masks (.bin)
│   ├── scene_001.bin      # Semantic class IDs (N×1 int64)
│   └── scene_002.bin
├── super_points/           # Superpoint assignments (.bin)
│   ├── scene_001.bin      # Superpoint IDs (N×1 int64)
│   └── scene_002.bin
├── bboxs/                  # 3D bounding boxes (.npy)
│   ├── scene_001.npy      # Bounding boxes (M×7 float32)
│   └── scene_002.npy
├── custom_infos_train.pkl  # Training scene metadata
├── custom_infos_val.pkl    # Validation scene metadata
└── custom_infos_test.pkl   # Test scene metadata

POINT CLOUD FORMAT:
- Points file: Binary file containing XYZRGB data
  - Shape: (N, 6) where N is number of points
  - Data type: float32
  - Columns: [X, Y, Z, R, G, B]
  - Coordinates: Metric units (meters)
  - Colors: 0-255 range

INSTANCE MASK FORMAT:
- Instance mask file: Binary file containing instance IDs
  - Shape: (N,) where N matches point cloud
  - Data type: int64
  - Values: Unique ID for each object instance
  - Background/unlabeled points: -1 or 0

SEMANTIC MASK FORMAT:
- Semantic mask file: Binary file containing semantic class IDs
  - Shape: (N,) where N matches point cloud
  - Data type: int64
  - Values: Class ID for each point
  - Background: 0, Objects: 1, 2, 3, ...

SUPERPOINTS FORMAT:
- Superpoints file: Binary file containing superpoint assignments
  - Shape: (N,) where N matches point cloud
  - Data type: int64
  - Values: Superpoint ID for each point
  - Can be generated automatically if not available

BOUNDING BOXES FORMAT:
- Bounding boxes file: NumPy array containing 3D boxes
  - Shape: (M, 7) where M is number of objects
  - Data type: float32
  - Columns: [center_x, center_y, center_z, width, length, height, yaw]
  - Coordinates: Same coordinate system as points
  - Yaw: Rotation around Z-axis in radians

CLASS MAPPING:
Common indoor object classes supported by UniDet3D:
- 0: background/floor/ceiling/wall
- 1: cabinet
- 2: bed
- 3: chair
- 4: sofa
- 5: table
- 6: door
- 7: window
- 8: bookshelf
- 9: picture
- 10: counter
- 11: desk
- 12: curtain
- 13: refrigerator
- 14: toilet
- 15: sink
- 16: bathtub
- 17: otherfurniture

COORDINATE SYSTEM:
- Right-handed coordinate system
- Z-axis points up
- Units in meters
- Origin can be arbitrary but should be consistent within each scene

PREPROCESSING PIPELINE:
1. Load your point cloud data (PLY, PCD, etc.)
2. Extract XYZ coordinates and RGB colors
3. Generate or load semantic/instance segmentation
4. Create 3D bounding boxes for objects
5. Generate superpoints (optional)
6. Convert to UniDet3D binary format
7. Create train/val/test splits
8. Generate metadata pkl files

EXAMPLE USAGE:
```python
from data_preprocessing_guide import UniDet3DDataConverter

# Initialize converter
converter = UniDet3DDataConverter("data/custom_dataset")

# Convert each scene
scene_infos = []
for scene_data in your_scenes:
    scene_info = converter.convert_point_cloud(
        points=scene_data['points'],           # (N, 3)
        colors=scene_data['colors'],           # (N, 3)
        semantic_labels=scene_data['semantic'], # (N,)
        instance_labels=scene_data['instance'], # (N,)
        bboxes_3d=scene_data['bboxes'],        # (M, 7)
        bbox_labels=scene_data['bbox_labels'], # (M,)
        scene_id=scene_data['id']
    )
    scene_infos.append(scene_info)

# Create train/val/test splits
converter.create_info_files(scene_infos)
```

VALIDATION CHECKLIST:
□ Point clouds are in XYZRGB format (6 channels)
□ All binary files use correct data types (float32/int64)
□ Instance and semantic masks have same length as points
□ Bounding boxes are in [x,y,z,w,l,h,yaw] format
□ Class labels are consistent across all files
□ Coordinate systems are aligned
□ File naming is consistent
□ Train/val/test splits are created
□ Metadata pkl files are generated

For more details, see the dataset-specific README files in the data/ directory.
"""
    
    print(guide)

def main():
    parser = argparse.ArgumentParser(description="UniDet3D Data Preprocessing Guide and Converter")
    parser.add_argument("--guide", action="store_true", help="Show data format guide")
    parser.add_argument("--convert", help="Convert custom data (provide input directory)")
    parser.add_argument("--output", help="Output directory for converted data")
    parser.add_argument("--format", choices=["ply", "pcd", "txt"], help="Input data format")
    
    args = parser.parse_args()
    
    if args.guide:
        print_data_format_guide()
    
    if args.convert:
        if not args.output:
            print("Error: --output directory required for conversion")
            return
        
        print(f"Converting data from {args.convert} to {args.output}")
        print("Note: This is a template. You need to implement the specific")
        print("conversion logic for your data format.")
        
        # Template for custom conversion
        converter = UniDet3DDataConverter(args.output)
        
        # TODO: Implement your specific data loading and conversion logic here
        # Example:
        # scene_infos = []
        # for scene_file in Path(args.convert).glob("*.ply"):
        #     # Load your data
        #     points, colors, semantic, instance, bboxes, labels = load_your_data(scene_file)
        #     
        #     # Convert to UniDet3D format
        #     scene_info = converter.convert_point_cloud(
        #         points=points,
        #         colors=colors,
        #         semantic_labels=semantic,
        #         instance_labels=instance,
        #         bboxes_3d=bboxes,
        #         bbox_labels=labels,
        #         scene_id=scene_file.stem
        #     )
        #     scene_infos.append(scene_info)
        # 
        # converter.create_info_files(scene_infos)
        
        print("Conversion template created. Please implement your specific data loading logic.")

if __name__ == "__main__":
    main()
