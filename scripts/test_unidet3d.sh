#!/bin/bash

# UniDet3D Model Testing Script
# This script provides comprehensive testing functionality for UniDet3D model

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"  # 获取项目根目录的绝对路径
CONFIG_FILE="configs/unidet3d_1xb8_scannet_s3dis_multiscan_3rscan_scannetpp_arkitscenes.py"
CHECKPOINT_FILE="checkpoints/unidet3d.pth"
WORK_DIR="work_dirs/test_results"
DEFAULT_GPU="1"  # 默认使用GPU #1

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to validate GPU configuration
validate_gpu() {
    local gpu_ids="$1"
    print_status "Validating GPU configuration: $gpu_ids"

    # Check if nvidia-smi is available
    if ! command -v nvidia-smi &> /dev/null; then
        print_error "nvidia-smi not found. GPU validation failed."
        exit 1
    fi

    # Get available GPUs
    local available_gpus=$(nvidia-smi --list-gpus | wc -l)
    print_status "Total GPUs available: $available_gpus"

    # Validate each specified GPU
    IFS=',' read -ra GPU_ARRAY <<< "$gpu_ids"
    for gpu_id in "${GPU_ARRAY[@]}"; do
        gpu_id=$(echo "$gpu_id" | xargs)  # trim whitespace
        if [[ ! "$gpu_id" =~ ^[0-9]+$ ]]; then
            print_error "Invalid GPU ID: $gpu_id (must be numeric)"
            exit 1
        fi

        if [ "$gpu_id" -ge "$available_gpus" ]; then
            print_error "GPU #$gpu_id not available (only 0-$((available_gpus-1)) available)"
            exit 1
        fi

        # Check if GPU is accessible
        if ! nvidia-smi -i "$gpu_id" &> /dev/null; then
            print_error "Cannot access GPU #$gpu_id"
            exit 1
        fi

        print_success "GPU #$gpu_id validated"
    done

    # Set CUDA_VISIBLE_DEVICES
    export CUDA_VISIBLE_DEVICES="$gpu_ids"
    print_status "CUDA_VISIBLE_DEVICES set to: $CUDA_VISIBLE_DEVICES"
}

# Function to check prerequisites
check_prerequisites() {
    local gpu_ids="$1"
    print_status "Checking prerequisites..."

    # Validate GPU configuration
    validate_gpu "$gpu_ids"

    # Check if we're in the correct directory
    if [ ! -f "$CONFIG_FILE" ]; then
        print_error "Configuration file not found: $CONFIG_FILE"
        print_error "Please run this script from the project root: $PROJECT_ROOT"
        exit 1
    fi

    # Check if checkpoint exists
    if [ ! -f "$CHECKPOINT_FILE" ]; then
        print_error "Checkpoint file not found: $CHECKPOINT_FILE"
        print_error "Please ensure the pre-trained model is available"
        exit 1
    fi
    
    # Check if datasets are available
    local datasets=("scannet" "s3dis" "multiscan" "3rscan" "scannetpp" "arkitscenes")
    local missing_datasets=()

    for ds in "${datasets[@]}"; do
        if [ ! -d "data/$ds" ]; then
            missing_datasets+=("$ds")
        fi
    done
    
    if [ ${#missing_datasets[@]} -gt 0 ]; then
        print_warning "Missing datasets: ${missing_datasets[*]}"
        print_warning "Please run setup_datasets.sh first to extract datasets"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_success "Prerequisites check completed"
}

# Function to run full model testing
test_full_model() {
    local gpu_ids="$1"
    local save_obj="$2"
    local save_dir="$3"
    local score_thr="$4"

    print_status "Starting full model testing on all 6 datasets..."
    print_status "Using GPU(s): $gpu_ids"

    if [ "$save_obj" = true ]; then
        print_status "Will save .obj files to: $save_dir"
        print_status "Using confidence threshold: $score_thr"
    fi

    # Create work directory
    mkdir -p "$WORK_DIR"

    # Determine launcher based on number of GPUs
    local launcher="none"
    local gpu_count=$(echo "$gpu_ids" | tr ',' '\n' | wc -l)
    if [ "$gpu_count" -gt 1 ]; then
        launcher="pytorch"
    fi

    # Build command
    local cmd="python tools/test.py \"$CONFIG_FILE\" \"$CHECKPOINT_FILE\" --work-dir \"$WORK_DIR\" --launcher \"$launcher\""

    # Add visualization options if saving .obj files
    if [ "$save_obj" = true ]; then
        mkdir -p "$save_dir"
        cmd="$cmd --show --show-dir \"$save_dir\" --score-thr $score_thr"
    fi

    # Run testing
    eval $cmd

    print_success "Full model testing completed"
    print_status "Results saved to: $WORK_DIR"

    if [ "$save_obj" = true ]; then
        print_success ".obj files saved to: $save_dir"
        print_status "Confidence threshold used: $score_thr"
        print_status "Open the .obj files in MeshLab for 3D visualization"
    fi
}

# Function to test individual dataset
test_single_dataset() {
    local dataset=$1
    local gpu_ids=$2
    local save_obj="$3"
    local save_dir="$4"
    local score_thr="$5"

    print_status "Testing on single dataset: $dataset"
    print_status "Using GPU(s): $gpu_ids"

    if [ "$save_obj" = true ]; then
        print_status "Will save .obj files to: $save_dir"
        print_status "Using confidence threshold: $score_thr"
    fi

    # Create dataset-specific config
    local single_config="configs/temp_${dataset}_config.py"

    # Create dataset-specific config using Python script
    python -c "
import sys
sys.path.append('.')
from mmengine.config import Config

# Load full config
cfg = Config.fromfile('$CONFIG_FILE')

# Dataset mapping - MUST match the order in config file
datasets = ['scannet', 's3dis', 'multiscan', '3rscan', 'scannetpp', 'arkitscenes']
target_dataset = '$dataset'

# Validate target dataset
if target_dataset not in datasets:
    print(f'Invalid dataset: {target_dataset}')
    print(f'Available datasets: {datasets}')
    sys.exit(1)

dataset_idx = datasets.index(target_dataset)
print(f'Selected dataset: {target_dataset} (index: {dataset_idx})')

# Check what's in the config before modification
print(f'Original config has {len(cfg.val_dataloader.dataset.datasets)} datasets')
for i, ds in enumerate(cfg.val_dataloader.dataset.datasets):
    print(f'  Dataset {i}: {ds.get(\"type\", \"Unknown\")} - {ds.get(\"ann_file\", \"No ann_file\")}')

# Update val_dataloader to include only the specified dataset
selected_dataset_config = cfg.val_dataloader.dataset.datasets[dataset_idx]
print(f'Selected dataset config: {selected_dataset_config.get(\"type\", \"Unknown\")} - {selected_dataset_config.get(\"ann_file\", \"No ann_file\")}')

cfg.val_dataloader.dataset.datasets = [selected_dataset_config]
cfg.test_dataloader = cfg.val_dataloader

# Update test_evaluator
cfg.test_evaluator.datasets = [target_dataset]
cfg.test_evaluator.datasets_classes = [cfg.test_evaluator.datasets_classes[dataset_idx]]
cfg.val_evaluator = cfg.test_evaluator

# Save temporary config
cfg.dump('$single_config')
print(f'Temporary config saved to: $single_config')
"

    # Run testing on single dataset
    local single_work_dir="$WORK_DIR/${dataset}_only"
    mkdir -p "$single_work_dir"

    # Determine launcher based on number of GPUs
    local launcher="none"
    local gpu_count=$(echo "$gpu_ids" | tr ',' '\n' | wc -l)
    if [ "$gpu_count" -gt 1 ]; then
        launcher="pytorch"
    fi

    # Build command
    local cmd="python tools/test.py \"$single_config\" \"$CHECKPOINT_FILE\" --work-dir \"$single_work_dir\" --launcher \"$launcher\""

    # Add visualization options if saving .obj files
    if [ "$save_obj" = true ]; then
        mkdir -p "$save_dir"
        cmd="$cmd --show --show-dir \"$save_dir\" --score-thr $score_thr"
    fi

    # Run testing
    eval $cmd

    # Clean up temporary config
    rm -f "$single_config"

    print_success "Single dataset testing completed for: $dataset"
    print_status "Results saved to: $single_work_dir"

    if [ "$save_obj" = true ]; then
        print_success ".obj files saved to: $save_dir"
        print_status "Confidence threshold used: $score_thr"
        print_status "Open the .obj files in MeshLab for 3D visualization"
    fi
}



# Function to display usage
show_usage() {
    echo "Usage: $0 [OPTION] [--gpu GPU_IDS] [--save-obj] [--save-dir DIR] [--score-thr THRESHOLD]"
    echo ""
    echo "Options:"
    echo "  full                    Test on all 6 datasets (default)"
    echo "  single DATASET         Test on single dataset"
    echo "                         Available: scannet, s3dis, multiscan, 3rscan, scannetpp, arkitscenes"
    echo "  check                  Check prerequisites only"
    echo "  help                   Show this help message"
    echo ""
    echo "GPU Configuration:"
    echo "  --gpu GPU_IDS          Specify GPU IDs to use (default: 1)"
    echo "                         Single GPU: --gpu 1"
    echo "                         Multiple GPUs: --gpu 1,2"
    echo ""
    echo "Save Options:"
    echo "  --save-obj             Save prediction results as .obj files for MeshLab visualization"
    echo "  --save-dir DIR         Directory to save .obj files (default: work_dirs/test_results/obj_files)"
    echo "  --score-thr THRESHOLD  Confidence threshold for saving predictions (default: 0.3)"
    echo "                         Higher values = fewer, more confident predictions"
    echo "                         Lower values = more predictions, including less confident ones"
    echo "                         Recommended range: 0.1 - 0.7"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Test all datasets"
    echo "  $0 single scannet --gpu 1            # Test ScanNet only"
    echo "  $0 single scannet --gpu 1 --save-obj # Test ScanNet and save .obj files"
    echo "  $0 single scannet --save-obj --score-thr 0.5  # Higher confidence threshold"
    echo "  $0 single scannet --save-obj --score-thr 0.1  # Lower confidence threshold"
    echo "  $0 full --gpu 1 --save-obj --score-thr 0.4    # Test all datasets with custom threshold"
    echo "  $0 check --gpu 1                     # Check prerequisites only"
}

# Function to parse arguments
parse_arguments() {
    local gpu_ids="$DEFAULT_GPU"
    local action=""
    local dataset=""
    local save_dir=""
    local save_obj=false
    local score_thr="0.3"
    local args=("$@")
    local i=0

    while [[ $i -lt ${#args[@]} ]]; do
        case "${args[$i]}" in
            --gpu)
                if [[ $((i+1)) -lt ${#args[@]} ]]; then
                    gpu_ids="${args[$((i+1))]}"
                    i=$((i+2))
                else
                    print_error "--gpu requires a value"
                    show_usage
                    exit 1
                fi
                ;;
            --save-dir)
                if [[ $((i+1)) -lt ${#args[@]} ]]; then
                    save_dir="${args[$((i+1))]}"
                    i=$((i+2))
                else
                    print_error "--save-dir requires a value"
                    show_usage
                    exit 1
                fi
                ;;
            --score-thr)
                if [[ $((i+1)) -lt ${#args[@]} ]]; then
                    score_thr="${args[$((i+1))]}"
                    i=$((i+2))
                else
                    print_error "--score-thr requires a value"
                    show_usage
                    exit 1
                fi
                ;;
            --save-obj)
                save_obj=true
                i=$((i+1))
                ;;
            full|single|check|help|-h|--help)
                action="${args[$i]}"
                i=$((i+1))
                ;;
            scannet|s3dis|multiscan|3rscan|scannetpp|arkitscenes)
                # Only set dataset if we're in single mode or no action set yet
                if [ "$action" = "single" ] || [ -z "$action" ]; then
                    dataset="${args[$i]}"
                fi
                i=$((i+1))
                ;;
            *)
                if [ -z "$action" ]; then
                    action="${args[$i]}"
                elif [ -z "$dataset" ] && [ "$action" = "single" ]; then
                    dataset="${args[$i]}"
                else
                    print_error "Unknown argument: ${args[$i]}"
                    show_usage
                    exit 1
                fi
                i=$((i+1))
                ;;
        esac
    done

    # Set default action if none specified
    [ -z "$action" ] && action="full"

    # Set default save directory if saving is enabled
    if [ "$save_obj" = true ] && [ -z "$save_dir" ]; then
        save_dir="work_dirs/test_results/obj_files"
    fi

    echo "$action|$dataset|$gpu_ids|$save_dir|$save_obj|$score_thr"
}

# Main execution
main() {
    echo "=== UniDet3D Model Testing ==="
    echo "Project: $PROJECT_ROOT"
    echo "Config: $CONFIG_FILE"
    echo "Checkpoint: $CHECKPOINT_FILE"
    echo ""

    # Parse arguments
    local parsed=$(parse_arguments "$@")
    IFS='|' read -r action dataset gpu_ids save_dir save_obj score_thr <<< "$parsed"

    echo "Action: $action"
    echo "Dataset: ${dataset:-all}"
    echo "GPU(s): $gpu_ids"
    [ "$save_obj" = true ] && echo "Save .obj files: Yes"
    [ "$save_obj" = true ] && echo "Confidence threshold: $score_thr"
    [ -n "$save_dir" ] && echo "Save directory: $save_dir"
    echo ""

    # Change to project directory
    cd "$PROJECT_ROOT"

    # Set Python path for UniDet3D module
    export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
    export OMP_NUM_THREADS=16

    case "$action" in
        "full")
            check_prerequisites "$gpu_ids"
            test_full_model "$gpu_ids" "$save_obj" "$save_dir" "$score_thr"
            ;;
        "single")
            if [ -z "$dataset" ]; then
                print_error "Dataset name required for single dataset testing"
                show_usage
                exit 1
            fi

            check_prerequisites "$gpu_ids"
            test_single_dataset "$dataset" "$gpu_ids" "$save_obj" "$save_dir" "$score_thr"
            ;;
        "check")
            check_prerequisites "$gpu_ids"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown option: $action"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
