# UniDet3D: 多数据集室内3D目标检测 - 完整操作指南

## 项目概述

UniDet3D是一个先进的多数据集室内3D目标检测方法，专门用于点云门窗检测任务。本项目在6个室内基准数据集上达到了最先进的性能，特别适用于室内场景的3D目标检测。

**核心特性**:
- 支持6个主流室内3D数据集的联合训练
- 专门优化的门窗检测能力
- 基于稀疏卷积的高效3D特征提取
- 多数据集联合训练策略
- 完整的数据预处理和训练测试流程

**支持的目标类别**:
- **门窗类别**: door (门), window (窗户)
- **家具类别**: cabinet (柜子), bed (床), chair (椅子), sofa (沙发), table (桌子), bookshelf (书架), desk (书桌)
- **设备类别**: refrigerator (冰箱), toilet (马桶), sink (水槽), bathtub (浴缸)
- **装饰类别**: picture (画), curtain (窗帘), counter (柜台)

## 环境配置

### 硬件要求
- **GPU**: RTX 4090 (推荐24GB显存)
- **内存**: 32GB以上
- **存储**: 500GB以上可用空间

### Docker环境
```bash
# 进入Docker容器 (必须在Docker容器内运行所有命令)
docker exec -it unidet3d_qb /bin/bash

# 项目路径
cd /home/<USER>/repos/unidet3d

# 设置环境变量 (脚本会自动设置，但手动运行时需要)
export PYTHONPATH=/home/<USER>/repos/unidet3d:$PYTHONPATH
export OMP_NUM_THREADS=16
```

**⚠️ 重要提醒**: 所有操作都必须在Docker容器内进行，不能在宿主机上运行！

### GPU配置说明
**服务器GPU配置**:
- 服务器配备4块RTX 4090 GPU (编号0, 1, 2, 3)
- **您被分配使用GPU #1** (其他GPU被其他用户占用)
- 所有脚本默认使用GPU #1，避免资源冲突

**GPU参数说明**:
- `--gpu 1`: 使用单个GPU #1 (默认)
- `--gpu 1,2`: 使用多个GPU (如果有权限)
- 脚本会自动验证GPU可用性和权限

### 依赖环境
项目基于以下主要依赖:
- PyTorch 2.1.2 + CUDA 12.1
- MMDetection3D 1.4.0
- MinkowskiEngine (稀疏卷积)
- SpConv 2.3.6
- Open3D 0.17.0

### 已知问题与解决方案

#### CUDA NMS 问题
**问题**: 运行时可能出现 `iou3d_nms3d_normal_forward_impl: implementation for device cuda:0 not found` 错误。

**原因**: MMCV 的 CUDA NMS 内核编译不完整。

**解决方案**:
- ✅ **自动回退**: 代码已实现纯 Python NMS 作为回退方案，确保功能正常
- 🔧 **永久修复**: 如需最佳性能，可重新编译 MMCV：
  ```bash
  cd /workspace/mmcv
  pip uninstall mmcv -y
  TORCH_CUDA_ARCH_LIST="6.1 7.0 8.9 9.0" MMCV_WITH_OPS=1 pip install -v -e . --no-deps
  ```

#### 数据集选择问题
**问题**: 已修复脚本中的变量作用域问题，确保正确选择用户指定的数据集。

**验证**: 运行 `./scripts/test_unidet3d.sh single scannet --gpu 1` 现在会正确测试 ScanNet 数据集。

#### 脚本权限问题
如果遇到权限错误，请确保脚本有执行权限：
```bash
chmod +x scripts/*.sh
```

## 数据准备

### 1. 数据集提取

项目支持6个数据集，数据文件位于 `/home/<USER>/data/unidet3d/`:
- `scannet.tar.gz` - ScanNet数据集
- `s3dis.tar.gz` - S3DIS数据集  
- `multiscan.tar.gz` - MultiScan数据集
- `3rscan.tar.gz` - 3RScan数据集
- `scannetpp.tar.gz` - ScanNet++数据集
- `arkitscenes.tar.gz` - ARKitScenes数据集

**自动提取脚本**:
```bash
# 使用提供的脚本自动提取所有数据集
chmod +x scripts/setup_datasets.sh
./scripts/setup_datasets.sh
```

**手动提取**:
```bash
# 创建数据目录
mkdir -p data

# 提取各个数据集
tar -xzf /home/<USER>/data/unidet3d/scannet.tar.gz -C data/
tar -xzf /home/<USER>/data/unidet3d/s3dis.tar.gz -C data/
tar -xzf /home/<USER>/data/unidet3d/multiscan.tar.gz -C data/
tar -xzf /home/<USER>/data/unidet3d/3rscan.tar.gz -C data/
tar -xzf /home/<USER>/data/unidet3d/scannetpp.tar.gz -C data/
tar -xzf /home/<USER>/data/unidet3d/arkitscenes.tar.gz -C data/
```

### 2. 数据结构验证

提取后的数据结构应如下所示:
```
data/
├── scannet/
│   ├── points/           # 点云文件 (.bin)
│   ├── instance_mask/    # 实例分割掩码 (.bin)
│   ├── semantic_mask/    # 语义分割掩码 (.bin)
│   ├── super_points/     # 超点分割 (.bin)
│   ├── meta_data/        # 元数据目录
│   └── *.pkl            # 训练/验证/测试信息文件
├── s3dis/
│   ├── points/           # 点云文件 (.bin)
│   ├── instance_mask/    # 实例分割掩码 (.bin)
│   ├── super_points/     # 超点分割 (.bin)
│   └── *.pkl            # 各区域信息文件
├── multiscan/
│   └── bins/            # 数据目录
│       ├── points/       # 点云文件 (.bin)
│       ├── instance_mask/ # 实例分割掩码 (.bin)
│       ├── semantic_mask/ # 语义分割掩码 (.bin)
│       ├── bboxs/        # 3D边界框 (.npy)
│       └── *.pkl        # 信息文件
├── 3rscan/
│   ├── points/           # 点云文件 (.bin)
│   ├── instance_mask/    # 实例分割掩码 (.bin)
│   ├── semantic_mask/    # 语义分割掩码 (.bin)
│   ├── super_points_spt/ # 超点分割 (.bin)
│   └── *.pkl            # 信息文件
├── scannetpp/
│   └── bins/            # 数据目录
│       ├── points/       # 点云文件 (.bin)
│       ├── instance_mask/ # 实例分割掩码 (.bin)
│       ├── semantic_mask/ # 语义分割掩码 (.bin)
│       ├── super_points_spt/ # 超点分割 (.bin)
│       └── *.pkl        # 信息文件
└── arkitscenes/
    ├── offline_prepared_data/ # 预处理数据目录
    ├── super_points/     # 超点分割 (.bin)
    └── *.pkl            # 信息文件
```

## 模型测试

### 1. 快速测试

**完整测试 (所有6个数据集)**:
```bash
# 使用自动化测试脚本 (默认使用GPU #1)
chmod +x scripts/test_unidet3d.sh
./scripts/test_unidet3d.sh full --gpu 1
```

**单数据集测试**:
```bash
# 测试特定数据集 (例如ScanNet)
./scripts/test_unidet3d.sh single scannet --gpu 1

# 支持的数据集: scannet, s3dis, multiscan, 3rscan, scannetpp, arkitscenes
```

**保存预测结果为.obj文件**:
```bash
# 测试所有数据集并保存.obj文件
./scripts/test_unidet3d.sh full --gpu 1 --save-obj

# 测试单个数据集并保存.obj文件
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj

# 自定义保存目录
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj --save-dir /path/to/save
```

**GPU验证**:
```bash
# 检查GPU配置和环境
./scripts/test_unidet3d.sh check --gpu 1
```

### 2. 手动测试命令

```bash
# 完整测试
python tools/test.py \
    configs/unidet3d_1xb8_scannet_s3dis_multiscan_3rscan_scannetpp_arkitscenes.py \
    checkpoints/unidet3d.pth \
    --work-dir work_dirs/test_results

# 带可视化的测试（保存.obj文件）
python tools/test.py \
    configs/unidet3d_1xb8_scannet_s3dis_multiscan_3rscan_scannetpp_arkitscenes.py \
    checkpoints/unidet3d.pth \
    --work-dir work_dirs/test_results \
    --show \
    --show-dir work_dirs/test_results/obj_files \
    --score-thr 0.3
```

### 3. 结果解读

**预期性能指标**:
| 数据集 | mAP@0.25 | mAP@0.50 |
|--------|----------|----------|
| ScanNet | 77.0 | 65.9 |
| ARKitScenes | 60.1 | 47.2 |
| S3DIS | 76.7 | 65.3 |
| MultiScan | 62.6 | 52.3 |
| 3RScan | 63.6 | 44.9 |
| ScanNet++ | 24.0 | 16.8 |

**结果分析工具**:
```bash
# 分析测试结果
python scripts/interpret_results.py work_dirs/test_results --output test_analysis.txt
```

## 预测结果保存和可视化

UniDet3D支持将预测结果保存为.obj格式文件，可在MeshLab中进行3D可视化分析。

### 1. 基本用法

**测试所有数据集并保存.obj文件**:
```bash
./scripts/test_unidet3d.sh full --gpu 1 --save-obj
```

**测试单个数据集并保存.obj文件**:
```bash
# 测试ScanNet数据集
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj

# 测试S3DIS数据集
./scripts/test_unidet3d.sh single s3dis --gpu 1 --save-obj

# 测试其他数据集
./scripts/test_unidet3d.sh single multiscan --gpu 1 --save-obj
./scripts/test_unidet3d.sh single 3rscan --gpu 1 --save-obj
./scripts/test_unidet3d.sh single scannetpp --gpu 1 --save-obj
./scripts/test_unidet3d.sh single arkitscenes --gpu 1 --save-obj
```

**自定义保存目录**:
```bash
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj --save-dir /path/to/custom/dir
```

**控制预测框数量（置信度阈值）**:
```bash
# 使用更高的置信度阈值，只保留高置信度的预测框（推荐用于清晰可视化）
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj --score-thr 0.5

# 使用较低的置信度阈值，保留更多预测框
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj --score-thr 0.1

# 默认阈值为0.3，平衡了预测数量和质量
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj
```

**置信度阈值说明**:
- `0.1`: 保留更多预测框，包括低置信度的（可能有噪声）
- `0.3`: 默认值，平衡预测数量和质量
- `0.5`: 只保留高置信度预测框，可视化更清晰
- `0.7`: 只保留非常高置信度的预测框，数量较少但质量很高

**重要说明**:
- 置信度阈值会同时影响模型的NMS过滤和可视化过滤
- 较高的阈值（如0.5-0.8）可以显著减少预测框数量，获得更清晰的可视化效果
- 如果您发现即使设置了高阈值仍有很多预测框，这是正常的，因为模型在高置信度下仍可能产生多个有效预测

### 2. 生成的文件结构

```
work_dirs/test_results/obj_files/
├── scannet/
│   ├── scene0019_00/
│   │   ├── scene0019_00_points.obj    # 点云数据
│   │   ├── scene0019_00_gt.obj        # 真值边界框
│   │   └── scene0019_00_pred.obj      # 预测边界框
│   └── ...
├── s3dis/
│   ├── Area_1_office_1/
│   │   ├── Area_1_office_1_points.obj
│   │   ├── Area_1_office_1_gt.obj
│   │   └── Area_1_office_1_pred.obj
│   └── ...
└── ...
```

### 3. MeshLab可视化

**打开文件**:
1. 启动MeshLab
2. 导入.obj文件: File → Import Mesh
3. 同时导入三个文件:
   - `*_points.obj`: 点云数据（彩色点）
   - `*_gt.obj`: 真值边界框（线框）
   - `*_pred.obj`: 预测边界框（线框）

**显示设置**:
1. 在右侧面板中调整显示选项
2. 启用点云颜色显示
3. 调整边界框为线框模式
4. 设置合适的点大小和线宽

**注意事项**:
- 默认使用 `score_thr=0.3`，可通过 `--score-thr` 参数调整
- 建议在Windows版MeshLab中打开（Mac版可能有颜色显示问题）
- 如果预测框太多显得杂乱，可以提高置信度阈值（如0.5或0.7）

### 4. 结果分析工具

```bash
# 分析保存的预测结果
python tools/analyze_predictions.py --results-dir work_dirs/test_results/obj_files --summary

# 分析特定数据集
python tools/analyze_predictions.py --results-dir work_dirs/test_results/obj_files --dataset scannet --summary
```

## 模型训练

### 1. 训练前准备

**检查环境**:
```bash
# 检查训练环境和数据 (包括GPU验证)
./scripts/train_unidet3d.sh check --gpu 1
```

**下载预训练骨干网络**:
```bash
# 确保骨干网络权重存在
ls work_dirs/tmp/oneformer3d_1xb4_scannet.pth
```

### 2. 训练选项

**完整训练 (6个数据集联合)**:
```bash
# 启动完整训练 (预计7-10天，使用GPU #1)
./scripts/train_unidet3d.sh full --gpu 1
```

**单数据集训练**:
```bash
# 训练单个数据集 (预计1-2天，使用GPU #1)
./scripts/train_unidet3d.sh single scannet --gpu 1
```

**自定义数据集组合训练**:
```bash
# 训练指定数据集组合 (使用GPU #1)
./scripts/train_unidet3d.sh custom scannet,s3dis,multiscan --gpu 1
```

### 3. 训练监控

**实时监控**:
```bash
# 监控训练进度
./scripts/train_unidet3d.sh monitor work_dirs/training_directory
```

**训练分析**:
```bash
# 分析训练日志和生成图表 (指定GPU)
python scripts/analyze_training.py work_dirs/training_directory \
    --plot-training --plot-validation \
    --save-plots work_dirs/training_directory/plots \
    --output training_report.txt \
    --gpu 1
```

### 4. 恢复训练

```bash
# 从检查点恢复训练 (指定GPU)
./scripts/train_unidet3d.sh resume work_dirs/training_directory --gpu 1
```

## 自定义数据预处理

### 1. 数据格式要求

UniDet3D要求以下数据格式:

**点云数据** (.bin文件):
- 格式: XYZRGB (6通道)
- 数据类型: float32
- 坐标单位: 米
- 颜色范围: 0-255
- 存储位置: `points/` 目录下

**分割掩码** (.bin文件):
- 实例掩码: int64类型，每个点的实例ID，存储在 `instance_mask/` 目录
- 语义掩码: int64类型，每个点的类别ID，存储在 `semantic_mask/` 目录

**超点分割** (.bin文件):
- 超点掩码: int64类型，每个点的超点ID
- 存储位置: `super_points/` 或 `super_points_spt/` 目录

**3D边界框** (.npy文件):
- 格式: [center_x, center_y, center_z, width, length, height, yaw]
- 数据类型: float32
- 存储位置: `bboxs/` 目录下 (部分数据集)

**元数据文件** (.pkl文件):
- 包含场景信息、标注信息、文件路径等
- 分为训练集、验证集、测试集三个文件

### 2. 数据转换工具

**查看数据格式指南**:
```bash
python scripts/data_preprocessing_guide.py --guide
```

**转换自定义数据**:
```python
import sys
sys.path.append('scripts')
from data_preprocessing_guide import UniDet3DDataConverter

# 初始化转换器
converter = UniDet3DDataConverter("data/custom_dataset")

# 转换点云数据
scene_info = converter.convert_point_cloud(
    points=your_points,           # (N, 3) XYZ坐标
    colors=your_colors,           # (N, 3) RGB颜色
    semantic_labels=semantic,     # (N,) 语义标签
    instance_labels=instance,     # (N,) 实例标签
    bboxes_3d=bboxes,            # (M, 7) 3D边界框
    bbox_labels=bbox_labels,     # (M,) 边界框类别
    scene_id="scene_001"
)
```

### 3. 门窗检测专用配置

对于门窗检测任务，重点关注以下类别:
- **door** (类别ID: 6) - 各种类型的门
- **window** (类别ID: 7) - 各种类型的窗户

可以通过修改配置文件中的类别权重来优化门窗检测性能。

## 常见问题解决

### 1. 内存不足
```bash
# 减少批次大小
# 修改配置文件中的 batch_size 参数
# 或使用梯度累积
```

### 2. 数据加载错误
```bash
# 检查数据路径配置
# 验证数据文件完整性
# 确认文件权限
```

### 3. 训练中断恢复
```bash
# 使用恢复训练功能
./scripts/train_unidet3d.sh resume work_dirs/interrupted_training
```

### 4. GPU相关问题
```bash
# 检查GPU状态
nvidia-smi

# 验证GPU权限
./scripts/test_unidet3d.sh check --gpu 1

# 如果GPU #1不可用，检查其他GPU
./scripts/test_unidet3d.sh check --gpu 0  # 尝试GPU #0
./scripts/test_unidet3d.sh check --gpu 2  # 尝试GPU #2
```

**常见GPU错误**:
- `GPU #X not available`: 指定的GPU不存在或被占用
- `Cannot access GPU #X`: GPU权限问题或驱动问题
- `CUDA out of memory`: GPU内存不足，尝试减少批次大小

### 5. 性能调优
- 调整学习率调度策略
- 修改数据增强参数
- 优化损失函数权重
- 调整NMS阈值

## 高级功能

### 1. 分布式训练
```bash
# 多GPU训练 (如果有多个GPU)
python -m torch.distributed.launch --nproc_per_node=2 \
    tools/train.py configs/unidet3d_config.py
```

### 2. 模型导出
```bash
# 导出ONNX模型
python tools/deployment/pytorch2onnx.py \
    configs/unidet3d_config.py \
    checkpoints/unidet3d.pth \
    --output-file unidet3d.onnx
```

### 3. 推理优化
```bash
# 使用TensorRT加速推理
python tools/deployment/test_torchscript.py \
    configs/unidet3d_config.py \
    checkpoints/unidet3d.pth
```

## 技术支持

如遇到问题，请按以下步骤排查:

1. **检查环境**: 确认Docker环境和依赖版本
2. **验证数据**: 确认数据集完整性和格式
3. **查看日志**: 检查训练/测试日志中的错误信息
4. **资源监控**: 确认GPU内存和磁盘空间充足
5. **参考文档**: 查阅原始论文和MMDetection3D文档

## 快速开始

### 基本使用流程
```bash
# 1. 进入Docker环境
docker exec -it unidet3d_qb /bin/bash
cd /home/<USER>/repos/unidet3d

# 2. 提取数据集
./scripts/setup_datasets.sh

# 3. 测试模型 (使用分配的GPU #1)
./scripts/test_unidet3d.sh full --gpu 1

# 4. 分析结果
python scripts/interpret_results.py work_dirs/test_results

# 5. 测试单个数据集并保存.obj文件用于可视化
./scripts/test_unidet3d.sh single scannet --gpu 1 --save-obj

# 6. 分析保存的预测结果
python tools/analyze_predictions.py --results-dir work_dirs/test_results/obj_files --summary
```

### 训练模型
```bash
# 检查环境 (包括GPU验证)
./scripts/train_unidet3d.sh check --gpu 1

# 开始训练 (使用GPU #1)
./scripts/train_unidet3d.sh full --gpu 1  # 完整训练
# 或
./scripts/train_unidet3d.sh single scannet --gpu 1  # 单数据集训练
```

### 自定义数据
```bash
# 查看数据格式指南
python scripts/data_preprocessing_guide.py --guide

# 转换自定义数据
python scripts/data_preprocessing_guide.py --convert /path/to/your/data --output data/custom
```

**⚠️ GPU使用提醒**:
- 所有训练和测试命令都必须指定 `--gpu 1` 参数
- 默认使用GPU #1，避免与其他用户冲突
- 脚本会自动验证GPU可用性和权限

所有脚本都已设置为可执行权限，位于 `scripts/` 目录下，可以直接运行。

## 引用

如果本项目对您的研究有帮助，请引用:

```bibtex
@inproceedings{kolodiazhnyi2025unidet3d,
  title={Unidet3d: Multi-dataset indoor 3d object detection},
  author={Kolodiazhnyi, Maksim and Vorontsova, Anna and Skripkin, Matvey and Rukhovich, Danila and Konushin, Anton},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={39},
  number={4},
  pages={4365--4373},
  year={2025}
}
```
